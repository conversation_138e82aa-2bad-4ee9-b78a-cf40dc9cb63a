Started by user admin
[Pipeline] Start of Pipeline
[Pipeline] podTemplate
[Pipeline] {
[Pipeline] node
Created Pod: kubernetes devops/c45e7b4d69fe4d4396e9e7879044a247-1-l958s-sgpv1-sr2r5
[PodInfo] devops/c45e7b4d69fe4d4396e9e7879044a247-1-l958s-sgpv1-sr2r5
	Container [jnlp] waiting [ContainerCreating] No message
	Pod [Pending][ContainersNotReady] containers with unready status: [jnlp]
[PodInfo] devops/c45e7b4d69fe4d4396e9e7879044a247-1-l958s-sgpv1-sr2r5
	Container [jnlp] waiting [ContainerCreating] No message
	Pod [Pending][ContainersNotReady] containers with unready status: [jnlp]
Still waiting to schedule task
‘c45e7b4d69fe4d4396e9e7879044a247-1-l958s-sgpv1-sr2r5’ is offline
[PodInfo] devops/c45e7b4d69fe4d4396e9e7879044a247-1-l958s-sgpv1-sr2r5
	Container [jnlp] waiting [ContainerCreating] No message
	Pod [Pending][ContainersNotReady] containers with unready status: [jnlp]
Agent c45e7b4d69fe4d4396e9e7879044a247-1-l958s-sgpv1-sr2r5 is provisioned from template c45e7b4d69fe4d4396e9e7879044a247_1-l958s-sgpv1
---
apiVersion: "v1"
kind: "Pod"
metadata:
  annotations:
    kubernetes.jenkins.io/last-refresh: "1754294112892"
    buildUrl: "http://ci-master:8080/job/c45e7b4d69fe4d4396e9e7879044a247/1/"
    runUrl: "job/c45e7b4d69fe4d4396e9e7879044a247/1/"
  labels:
    app: "jenkins-slave"
    jenkins: "slave"
    jenkins/label-digest: "aaa95735230fc62fc5c11b51625a8511579bdb83"
    jenkins/label: "c45e7b4d69fe4d4396e9e7879044a247_1-l958s"
    kubernetes.jenkins.io/controller: "http___ci-master_8080x"
  name: "c45e7b4d69fe4d4396e9e7879044a247-1-l958s-sgpv1-sr2r5"
spec:
  containers:
  - env:
    - name: "JENKINS_SECRET"
      value: "********"
    - name: "JENKINS_AGENT_NAME"
      value: "c45e7b4d69fe4d4396e9e7879044a247-1-l958s-sgpv1-sr2r5"
    - name: "REMOTING_OPTS"
      value: "-noReconnectAfter 1d"
    - name: "JENKINS_NAME"
      value: "c45e7b4d69fe4d4396e9e7879044a247-1-l958s-sgpv1-sr2r5"
    - name: "JENKINS_AGENT_WORKDIR"
      value: "/home/<USER>/agent"
    - name: "JENKINS_URL"
      value: "http://ci-master:8080/"
    image: "***********:20200/devops/jenkins-slave:v3"
    imagePullPolicy: "Always"
    name: "jnlp"
    resources:
      limits:
        cpu: "8"
        memory: "8Gi"
      requests:
        cpu: "400m"
        memory: "2048Mi"
    securityContext:
      privileged: true
      runAsUser: 0
    volumeMounts:
    - mountPath: "/etc/buildkitd.toml"
      name: "config"
      subPath: "buildkitd.toml"
    - mountPath: "/etc/docker/daemon.json"
      name: "config"
      subPath: "daemon.json"
    - mountPath: "/home/<USER>/agent"
      name: "workspace-volume"
      readOnly: false
  hostAliases:
  - hostnames:
    - "sgyf-luchi-pod1-core-157-199"
    ip: "**************"
  - hostnames:
    - "sgyf-luchi-pod1-core-157-200"
    ip: "**************"
  nodeSelector:
    kubernetes.io/os: "linux"
  restartPolicy: "Never"
  volumes:
  - emptyDir:
      medium: ""
    name: "workspace-volume"
  - configMap:
      defaultMode: 420
      items:
      - key: "buildkitd.toml"
        path: "buildkitd.toml"
      - key: "daemon.json"
        path: "daemon.json"
      name: "inbound-agent-config"
    name: "config"

Running on c45e7b4d69fe4d4396e9e7879044a247-1-l958s-sgpv1-sr2r5 in /home/<USER>/agent/workspace/c45e7b4d69fe4d4396e9e7879044a247
[Pipeline] {
[Pipeline] timeout
Timeout set to expire in 10 min
[Pipeline] {
[Pipeline] stage
[Pipeline] { (1-代码检出)
[Pipeline] checkout
The recommended git tool is: NONE
using credential c45e7b4d69fe4d4396e9e7879044a247
Cloning the remote Git repository
Cloning repository http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/cmake-3.10.1.git
 > git init /home/<USER>/agent/workspace/c45e7b4d69fe4d4396e9e7879044a247 # timeout=10
Fetching upstream changes from http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/cmake-3.10.1.git
 > git --version # timeout=10
 > git --version # 'git version 2.49.0'
using GIT_ASKPASS to set credentials 
 > git fetch --tags --force --progress -- http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/cmake-3.10.1.git +refs/heads/*:refs/remotes/origin/* # timeout=10
Avoid second fetch
Checking out Revision f1f8007c2dcfdc8e97b34be85ce1a123ecf0e7b3 (origin/main)
 > git config remote.origin.url http://code.tiancloud.com:9002/SITECH-DevOps/DevOpsV1.5.0/sigong/builddemo/cmake-3.10.1.git # timeout=10
 > git config --add remote.origin.fetch +refs/heads/*:refs/remotes/origin/* # timeout=10
 > git rev-parse origin/main^{commit} # timeout=10
 > git config core.sparsecheckout # timeout=10
 > git checkout -f f1f8007c2dcfdc8e97b34be85ce1a123ecf0e7b3 # timeout=10
Commit message: "补充缺失文件"
First time build. Skipping changelog.
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (2-下载构建依赖缓存 )
[Pipeline] echo
下载构建缓存
[Pipeline] withEnv
[Pipeline] {
[Pipeline] script
[Pipeline] {
[Pipeline] sh
[Pipeline] sh
+ aws s3 ls s3://build-cache/a054f33c7fa44c9eb0bf47a0ba251ad0/dependency-cache/cache.tar.gz --endpoint-url http://************:9000
[Pipeline] }
[Pipeline] // script
[Pipeline] }
[Pipeline] // withEnv
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (CMake构建-1)
[Pipeline] getContext
[Pipeline] isUnix
[Pipeline] withEnv
[Pipeline] {
[Pipeline] sh
+ docker inspect -f . ***********:20200/devops-test/cmake-ccache-icecream:cmake3.10.1-gcc5.5.0

Error: No such object: ***********:20200/devops-test/cmake-ccache-icecream:cmake3.10.1-gcc5.5.0
[Pipeline] isUnix
[Pipeline] withEnv
[Pipeline] {
[Pipeline] sh
+ docker pull ***********:20200/devops-test/cmake-ccache-icecream:cmake3.10.1-gcc5.5.0
cmake3.10.1-gcc5.5.0: Pulling from devops-test/cmake-ccache-icecream
2d473b07cdd5: Pulling fs layer
1ed8863d47a4: Pulling fs layer
84e8365b39b9: Pulling fs layer
5f6aaabe5cfe: Pulling fs layer
2a41e353fac0: Pulling fs layer
311e435eb4ac: Pulling fs layer
4ae8bf25daf2: Pulling fs layer
de0bdfd66e5f: Pulling fs layer
9948dade59ae: Pulling fs layer
5f6aaabe5cfe: Waiting
122f08a69f6f: Pulling fs layer
311e435eb4ac: Waiting
0d4e1d1f49ff: Pulling fs layer
4ae8bf25daf2: Waiting
c8f179fe31df: Pulling fs layer
2a41e353fac0: Waiting
de0bdfd66e5f: Waiting
f3ec66c4b8ec: Pulling fs layer
122f08a69f6f: Waiting
8c8da586d1e0: Pulling fs layer
9948dade59ae: Waiting
414f2780b24c: Pulling fs layer
ad814ac9b65a: Pulling fs layer
c8f179fe31df: Waiting
0d4e1d1f49ff: Waiting
f3ec66c4b8ec: Waiting
9b2af19828f5: Pulling fs layer
8c8da586d1e0: Waiting
414f2780b24c: Waiting
4f4fb700ef54: Pulling fs layer
ad814ac9b65a: Waiting
17352abbc77b: Pulling fs layer
01ab15df99b5: Pulling fs layer
4f4fb700ef54: Waiting
9b2af19828f5: Waiting
ad33680be33e: Pulling fs layer
17352abbc77b: Waiting
01ab15df99b5: Waiting
ad33680be33e: Waiting
a1609be524a5: Pulling fs layer
d19ca63b437b: Pulling fs layer
a1609be524a5: Waiting
d19ca63b437b: Waiting
d0934fece71d: Pulling fs layer
1f1f9bb3f38d: Pulling fs layer
0fcf46f03dbe: Pulling fs layer
0fcf46f03dbe: Waiting
d0934fece71d: Waiting
1f1f9bb3f38d: Waiting
eb61e233ee2c: Pulling fs layer
7f4a0603d51c: Pulling fs layer
7920f5be97fe: Pulling fs layer
eb61e233ee2c: Waiting
c5816c84d762: Pulling fs layer
7920f5be97fe: Waiting
08eb02a6b5c6: Pulling fs layer
7f4a0603d51c: Waiting
ae0be3b4910b: Pulling fs layer
94fb8244dd33: Pulling fs layer
c5816c84d762: Waiting
2e4013653df5: Pulling fs layer
385fcb0dfdb7: Pulling fs layer
8c39cc8aa0ad: Pulling fs layer
94fb8244dd33: Waiting
ae0be3b4910b: Waiting
0e89d3d20dc8: Pulling fs layer
08eb02a6b5c6: Waiting
b5fc2c87277a: Pulling fs layer
0e89d3d20dc8: Waiting
2e4013653df5: Waiting
385fcb0dfdb7: Waiting
8c39cc8aa0ad: Waiting
94865f5d87de: Pulling fs layer
b5fc2c87277a: Waiting
94865f5d87de: Waiting
56f737da6af6: Pulling fs layer
56f737da6af6: Waiting
84e8365b39b9: Verifying Checksum
84e8365b39b9: Download complete
1ed8863d47a4: Download complete
2a41e353fac0: Verifying Checksum
2a41e353fac0: Download complete
5f6aaabe5cfe: Verifying Checksum
5f6aaabe5cfe: Download complete
2d473b07cdd5: Verifying Checksum
2d473b07cdd5: Download complete
de0bdfd66e5f: Verifying Checksum
de0bdfd66e5f: Download complete
311e435eb4ac: Verifying Checksum
311e435eb4ac: Download complete
4ae8bf25daf2: Verifying Checksum
4ae8bf25daf2: Download complete
0d4e1d1f49ff: Verifying Checksum
0d4e1d1f49ff: Download complete
c8f179fe31df: Verifying Checksum
c8f179fe31df: Download complete
122f08a69f6f: Verifying Checksum
122f08a69f6f: Download complete
9948dade59ae: Verifying Checksum
9948dade59ae: Download complete
414f2780b24c: Verifying Checksum
414f2780b24c: Download complete
ad814ac9b65a: Download complete
9b2af19828f5: Download complete
4f4fb700ef54: Verifying Checksum
4f4fb700ef54: Download complete
17352abbc77b: Download complete
01ab15df99b5: Verifying Checksum
01ab15df99b5: Download complete
ad33680be33e: Download complete
a1609be524a5: Verifying Checksum
a1609be524a5: Download complete
d19ca63b437b: Verifying Checksum
d19ca63b437b: Download complete
d0934fece71d: Verifying Checksum
d0934fece71d: Download complete
1f1f9bb3f38d: Download complete
0fcf46f03dbe: Download complete
8c8da586d1e0: Verifying Checksum
8c8da586d1e0: Download complete
7f4a0603d51c: Verifying Checksum
7f4a0603d51c: Download complete
7920f5be97fe: Download complete
c5816c84d762: Verifying Checksum
c5816c84d762: Download complete
08eb02a6b5c6: Verifying Checksum
08eb02a6b5c6: Download complete
ae0be3b4910b: Verifying Checksum
ae0be3b4910b: Download complete
94fb8244dd33: Download complete
2e4013653df5: Download complete
385fcb0dfdb7: Download complete
8c39cc8aa0ad: Download complete
f3ec66c4b8ec: Verifying Checksum
f3ec66c4b8ec: Download complete
b5fc2c87277a: Verifying Checksum
b5fc2c87277a: Download complete
0e89d3d20dc8: Verifying Checksum
0e89d3d20dc8: Download complete
94865f5d87de: Verifying Checksum
94865f5d87de: Download complete
56f737da6af6: Verifying Checksum
56f737da6af6: Download complete
eb61e233ee2c: Verifying Checksum
eb61e233ee2c: Download complete
2d473b07cdd5: Pull complete
1ed8863d47a4: Pull complete
84e8365b39b9: Pull complete
5f6aaabe5cfe: Pull complete
2a41e353fac0: Pull complete
311e435eb4ac: Pull complete
4ae8bf25daf2: Pull complete
de0bdfd66e5f: Pull complete
9948dade59ae: Pull complete
122f08a69f6f: Pull complete
0d4e1d1f49ff: Pull complete
c8f179fe31df: Pull complete
f3ec66c4b8ec: Pull complete
8c8da586d1e0: Pull complete
414f2780b24c: Pull complete
ad814ac9b65a: Pull complete
9b2af19828f5: Pull complete
4f4fb700ef54: Pull complete
17352abbc77b: Pull complete
01ab15df99b5: Pull complete
ad33680be33e: Pull complete
a1609be524a5: Pull complete
d19ca63b437b: Pull complete
d0934fece71d: Pull complete
1f1f9bb3f38d: Pull complete
0fcf46f03dbe: Pull complete
eb61e233ee2c: Pull complete
7f4a0603d51c: Pull complete
7920f5be97fe: Pull complete
c5816c84d762: Pull complete
08eb02a6b5c6: Pull complete
ae0be3b4910b: Pull complete
94fb8244dd33: Pull complete
2e4013653df5: Pull complete
385fcb0dfdb7: Pull complete
8c39cc8aa0ad: Pull complete
0e89d3d20dc8: Pull complete
b5fc2c87277a: Pull complete
94865f5d87de: Pull complete
56f737da6af6: Pull complete
Digest: sha256:ab611cb566766a19901c1dc67df3f3a914b865a1d2aa7b35449b754f7a353dae
Status: Downloaded newer image for ***********:20200/devops-test/cmake-ccache-icecream:cmake3.10.1-gcc5.5.0
***********:20200/devops-test/cmake-ccache-icecream:cmake3.10.1-gcc5.5.0
[Pipeline] }
[Pipeline] // withEnv
[Pipeline] }
[Pipeline] // withEnv
[Pipeline] withDockerContainer
c45e7b4d69fe4d4396e9e7879044a247-1-l958s-sgpv1-sr2r5 seems to be running inside container bed13e3155fefc8cbce79374ee767a6230ede904836dff424ef77c366f295489
but /home/<USER>/agent/workspace/c45e7b4d69fe4d4396e9e7879044a247 could not be found among []
but /home/<USER>/agent/workspace/c45e7b4d69fe4d4396e9e7879044a247@tmp could not be found among []
$ docker run -t -d -u 0:0 -v ${WORKSPACE}:/workspace -w /workspace --network host -e USE_DISTRIBUTED_BUILD=true -v /root/.ccache:/root/.ccache -v /root/.cache:/root/.cache -v /go/pkg/mod:/go/pkg/mod -v /root/.yarn:/root/.yarn -v /root/.npm:/root/.npm -v /root/.gradle/caches:/root/.gradle/caches -v /root/.m2:/root/.m2 -w /home/<USER>/agent/workspace/c45e7b4d69fe4d4396e9e7879044a247 -v /home/<USER>/agent/workspace/c45e7b4d69fe4d4396e9e7879044a247:/home/<USER>/agent/workspace/c45e7b4d69fe4d4396e9e7879044a247:rw,z -v /home/<USER>/agent/workspace/c45e7b4d69fe4d4396e9e7879044a247@tmp:/home/<USER>/agent/workspace/c45e7b4d69fe4d4396e9e7879044a247@tmp:rw,z -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** -e ******** ***********:20200/devops-test/cmake-ccache-icecream:cmake3.10.1-gcc5.5.0 cat
$ docker top 6d96bc8c2fad878fcce9425a081fee302d268f97899077929037c75ff59fd350 -eo pid,comm
[Pipeline] {
[Pipeline] script
[Pipeline] {
[Pipeline] echo
Step 1: icecream分布式集群环境初始化
[Pipeline] echo
生成Docker Compose配置文件...
[Pipeline] sh
+ hostname -I
+ awk '{print $1}'
[Pipeline] echo
检测到宿主机IP: *************
[Pipeline] writeFile
[Pipeline] echo
开始执行Docker Compose操作...
[Pipeline] sh
+ docker-compose -f ./tmp/docker-compose.yml up -d
/home/<USER>/agent/workspace/c45e7b4d69fe4d4396e9e7879044a247@tmp/durable-beb1e944/script.sh.copy: line 1: docker-compose: command not found
[Pipeline] }
[Pipeline] // script
[Pipeline] }
$ docker stop --time=1 6d96bc8c2fad878fcce9425a081fee302d268f97899077929037c75ff59fd350
$ docker rm -f --volumes 6d96bc8c2fad878fcce9425a081fee302d268f97899077929037c75ff59fd350
[Pipeline] // withDockerContainer
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (4-上传构建依赖缓存 )
Stage "4-上传构建依赖缓存 " skipped due to earlier failure(s)
[Pipeline] getContext
[Pipeline] }
[Pipeline] // stage
[Pipeline] stage
[Pipeline] { (Declarative: Post Actions)
[Pipeline] script
[Pipeline] {
[Pipeline] writeFile
[Pipeline] archiveArtifacts
Archiving artifacts
[Pipeline] }
[Pipeline] // script
[Pipeline] }
[Pipeline] // stage
[Pipeline] }
[Pipeline] // timeout
[Pipeline] }
[Pipeline] // node
[Pipeline] }
[Pipeline] // podTemplate
[Pipeline] End of Pipeline
ERROR: script returned exit code 127
Finished: FAILURE
