package com.cmcc.cmdevops.ci.adapter.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
public class BuildEnvironmentDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String environmentName;

    private String environmentType;

    private String environmentDesc;

    private String region;

    /**
     * 构建环境地址
     */
    private String environmentUrl;

    /**
     * 构建环境配置
     */
    private String environmentConfig;

    /**
     * 是否依赖缓存
     */
    private Boolean isUseCache;

    /**
     * 缓存地址
     */
    private String cacheDir;

    /**
     * 访问密钥
     */
    private String accessKey;

    /**
     * 秘密密钥
     */
    private String secretKey;

    private String spaceId;

    private String tenantId;

    private Boolean deleted;

    private LocalDateTime createTime;

    private String createUid;

    private LocalDateTime updateTime;

    private String updateUid;

    private LocalDateTime deleteTime;

    private String deleteUid;

    private String accelerationNode;

    private String environmentSource;

    private String status;

    private Boolean projectAuth;

    private Boolean spaceAuth;
}
