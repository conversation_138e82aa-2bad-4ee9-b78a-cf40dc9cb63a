package com.cmcc.cmdevops.ci.adapter.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2025-05-20
 */
@Getter
@Setter
@Builder
@Accessors(chain = true)
public class BuildEnvironmentPermissionDTO implements Serializable {
    public BuildEnvironmentPermissionDTO() {
    }

    private static final long serialVersionUID = 1L;

    public BuildEnvironmentPermissionDTO(Integer id, Integer environmentId, String type, String businessDataId, String businessDataName, String tenantId, Boolean deleted, LocalDateTime createTime, String createUid, LocalDateTime updateTime, String updateUid, LocalDateTime deleteTime, String deleteUid, List<String> spaces) {
        this.id = id;
        this.environmentId = environmentId;
        this.type = type;
        this.businessDataId = businessDataId;
        this.businessDataName = businessDataName;
        this.tenantId = tenantId;
        this.deleted = deleted;
        this.createTime = createTime;
        this.createUid = createUid;
        this.updateTime = updateTime;
        this.updateUid = updateUid;
        this.deleteTime = deleteTime;
        this.deleteUid = deleteUid;
        this.spaces = spaces;
    }

    private Integer id;

    private Integer environmentId;

    private String type;

    private String businessDataId;

    private String businessDataName;

    private String tenantId;

    private Boolean deleted;

    private LocalDateTime createTime;

    private String createUid;

    private LocalDateTime updateTime;

    private String updateUid;

    private LocalDateTime deleteTime;

    private String deleteUid;

    private List<String> spaces;
}
