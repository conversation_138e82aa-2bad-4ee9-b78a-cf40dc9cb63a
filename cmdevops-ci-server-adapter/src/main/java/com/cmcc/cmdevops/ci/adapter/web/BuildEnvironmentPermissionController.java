package com.cmcc.cmdevops.ci.adapter.web;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.cmcc.cmdevops.BaseResponse;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.adapter.dto.BuildEnvironmentPermissionDTO;
import com.cmcc.cmdevops.ci.adapter.dto.BuildPermissionDTO;
import com.cmcc.cmdevops.ci.adapter.dto.BuildTaskDTO;
import com.cmcc.cmdevops.ci.adapter.dto.request.BuildPermissionPageRequest;
import com.cmcc.cmdevops.ci.service.bo.BuildEnvironmentPermissionBO;
import com.cmcc.cmdevops.ci.service.bo.BuildPermissionBO;
import com.cmcc.cmdevops.ci.service.bo.BuildTaskBO;
import com.cmcc.cmdevops.ci.service.business.BuildEnvironmentPermissionBizService;
import com.cmcc.cmdevops.ci.service.business.BuildPermissionBizService;
import com.cmcc.cmdevops.ci.service.business.SWorkerOpenBizService;
import com.cmcc.cmdevops.util.BeanCloner;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 注意这是已经使用swagger3.0版本
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/environment_permission")
@RequiredArgsConstructor
public class BuildEnvironmentPermissionController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BuildEnvironmentPermissionController.class);

    @Resource
    private BuildEnvironmentPermissionBizService buildEnvironmentPermissionBizService;

    @Resource
    private SWorkerOpenBizService sWorkerOpenBizService;

    @PostMapping("/list")
    public BaseResponse<List<BuildEnvironmentPermissionDTO>> list(@RequestBody BuildEnvironmentPermissionDTO dto) {
        List<BuildEnvironmentPermissionBO> list = buildEnvironmentPermissionBizService.list(BeanCloner.clone(dto, BuildEnvironmentPermissionBO.class));
        List<BuildEnvironmentPermissionDTO> clone = BeanCloner.clone(list, BuildEnvironmentPermissionDTO.class);
        if (dto.getType().equals("project")) {
            for (BuildEnvironmentPermissionDTO buildEnvironmentPermissionDTO : clone) {
                JSONArray spaces = sWorkerOpenBizService.queryProjectSpaceRel(buildEnvironmentPermissionDTO.getTenantId(), buildEnvironmentPermissionDTO.getBusinessDataId());
                List<String> spaceList = new ArrayList<>();
                for (int i = 0; i < spaces.size(); i++) {
                    JSONObject jsonObject = spaces.getJSONObject(i);
                    spaceList.add(jsonObject.getString("spaceName"));
                }
                buildEnvironmentPermissionDTO.setSpaces(spaceList);
            }
        }
        return BaseResponse.success(clone);
    }

    @PostMapping("/save")
    public BaseResponse<Void> save(@RequestBody List<BuildEnvironmentPermissionDTO> dtos){
        List<BuildEnvironmentPermissionBO> bos = BeanCloner.clone(dtos, BuildEnvironmentPermissionBO.class);
        buildEnvironmentPermissionBizService.save(bos);
        return BaseResponse.success();
    }


    @DeleteMapping("/delete/{id}")
    public BaseResponse<Void> delete(@PathVariable String id){
        buildEnvironmentPermissionBizService.delete(id);
        return BaseResponse.success();
    }
}
