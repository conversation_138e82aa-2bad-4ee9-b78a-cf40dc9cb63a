package com.cmcc.cmdevops.ci.service.atom.impl;

import com.cmcc.cmdevops.ci.service.dao.BuildEnvironmentPermissionDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildEnvironmentPermissionMapper;
import com.cmcc.cmdevops.ci.service.atom.BuildEnvironmentPermissionAtomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-08-01
 */
@Service
public class BuildEnvironmentPermissionAtomServiceImpl extends ServiceImpl<BuildEnvironmentPermissionMapper, BuildEnvironmentPermissionDO> implements BuildEnvironmentPermissionAtomService {

}
