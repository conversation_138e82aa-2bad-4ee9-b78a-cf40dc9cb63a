package com.cmcc.cmdevops.ci.service.dao;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("build_environment")
public class BuildEnvironmentDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 环境名称
     */
    @TableField("environment_name")
    private String environmentName;

    /**
     * 环境类型
     */
    @TableField("environment_type")
    private String environmentType;

    /**
     * 环境描述
     */
    @TableField("environment_desc")
    private String environmentDesc;

    /**
     * 所属区域
     */
    @TableField("region")
    private String region;

    /**
     * 构建环境地址
     */
    @TableField("environment_url")
    private String environmentUrl;

    /**
     * 构建环境配置
     */
    @TableField("environment_config")
    private String environmentConfig;

    /**
     * 是否依赖缓存
     */
    @TableField("is_use_cache")
    private Boolean isUseCache;

    /**
     * 缓存地址
     */
    @TableField("cache_dir")
    private String cacheDir;

    /**
     * 访问密钥
     */
    @TableField("access_key")
    private String accessKey;

    /**
     * 秘密密钥
     */
    @TableField("secret_key")
    private String secretKey;

    @TableField("space_id")
    private String spaceId;

    @TableField("tenant_id")
    private String tenantId;

    @TableField("deleted")
    @TableLogic
    private Boolean deleted;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField("create_uid")
    private String createUid;

    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    @TableField("update_uid")
    private String updateUid;

    @TableField("delete_time")
    private LocalDateTime deleteTime;

    @TableField("delete_uid")
    private String deleteUid;

    @TableField("acceleration_node")
    private String accelerationNode;

    @TableField("environment_source")
    private String environmentSource;

    @TableField("status")
    private String status;

    @TableField("project_auth")
    private Boolean projectAuth;

    @TableField("space_auth")
    private Boolean spaceAuth;
}
