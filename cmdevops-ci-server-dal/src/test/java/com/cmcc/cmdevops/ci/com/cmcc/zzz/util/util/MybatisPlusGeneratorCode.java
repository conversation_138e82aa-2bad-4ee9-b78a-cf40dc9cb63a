package com.cmcc.cmdevops.ci.com.cmcc.zzz.util.util;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.IFill;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.fill.Column;
import org.apache.ibatis.annotations.Mapper;

import java.util.HashMap;

/**
 * 主要用于mybatis plus相关文件的生成。优先使用这个,即优先使用mybatis plus模式
 */
public class MybatisPlusGeneratorCode {
    public static void main(String[] args) {
        // 代码生成目录
        // 获取当前子模块的路径
        String projectPath = System.getProperty("user.dir"); // 当前模块的工作目录
        String moduleName = "cmdevops-ci-server-dal";
        String output = projectPath + "/" + moduleName + "/src/main/java";   // 目标 Java 文件路径
        // 自动填充
        IFill createTime = new Column("create_time", FieldFill.INSERT);
        IFill updateTime = new Column("modify_time", FieldFill.INSERT_UPDATE);
        FastAutoGenerator.create("******************************************************************************************************************************************************************************************************************", "devops", "devops_Pipline@")
                .globalConfig(builder -> {
                    builder.author("")      // 作者
                            .outputDir(output)  // 输出路径
                            .disableOpenDir();      // 生成后不打开目录
                })
                .packageConfig(builder -> builder.moduleName("") // 模块名
                        .parent("com.cmcc.cmdevops.ci.service")   // 父包名
                        .entity("dao")
                        .mapper("dao.mapper")
                        .service("atom")
                        .serviceImpl("atom.impl")
                        // 完全禁用XML生成,不设置任何XML输出路径
                        .pathInfo(new HashMap<>()))
                // 明确禁用XML模板
                .templateConfig(builder -> builder.xml(null).disable())
                .strategyConfig(builder -> builder.addInclude("build_environment_permission") // 要生成的表
                        .addTablePrefix("t_")
                        .entityBuilder()
                        .enableFileOverride()
                        .naming(NamingStrategy.underline_to_camel)
                        .columnNaming(NamingStrategy.underline_to_camel)
                        .enableChainModel()// 实体类配置
                        .formatFileName("%sDO") // 设置实体类名称后缀为BO
                        .enableTableFieldAnnotation()
                        .logicDeleteColumnName("deleted")
                        .logicDeletePropertyName("deleted")
                        .versionColumnName("version")
                        .versionPropertyName("version")
                        .addTableFills(createTime, updateTime)
                        .enableLombok()         // 启用 Lombok
                        .controllerBuilder()
                        .enableRestStyle()// Controller 配置
                        .enableHyphenStyle()
                        .enableRestStyle()
                        .disable()// 生成 @RestController
                        .mapperBuilder()
                        .enableFileOverride()
                        .superClass(BaseMapper.class)
                        .mapperAnnotation(Mapper.class)
                        .enableBaseResultMap()// Mapper 配置
                        .enableBaseColumnList()// 添加 @Mapper 注解
                        .serviceBuilder()
                        .enableFileOverride()
                        .superServiceClass(IService.class)
                        .formatServiceFileName("%sAtomService")
                        .superServiceImplClass(ServiceImpl.class)
                        .formatServiceImplFileName("%sAtomServiceImpl"))
                .execute();
    }
}

