package com.cmcc.cmdevops.ci.properties;

import com.cmcc.cmdevops.ci.service.bo.BuildToolBO;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.EnumerablePropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 构建镜像配置属性类
 * 
 * <p>该配置类用于映射application-tool.yml中的build-tools配置项，
 * 包含了各种构建工具及其对应的Docker镜像列表。</p>
 * 
 * <p>配置结构示例：</p>
 * <pre>
 * build-tools:
 *   android:
 *     - gradle7.2#10.86.12.11:20200/devops-test/android-sdkman:v1
 *     - gradle7.3#10.86.12.11:20200/devops-test/android-sdkman:v1
 *   gradle:
 *     - gradle7.2#10.86.12.11:20200/devops-test/gradle-sdkman:v1
 *     - gradle7.3#10.86.12.11:20200/devops-test/gradle-sdkman:v1
 * </pre>
 * 
 * <p>使用方式：</p>
 * <pre>
 * &#64;Autowired
 * private BuildImagesProperties buildImagesProperties;
 * 
 * // 获取Android构建镜像列表
 * List&lt;String&gt; androidImages = buildImagesProperties.getBuildImages().get("android");
 * 
 * // 获取所有构建工具
 * Set&lt;String&gt; allTools = buildImagesProperties.getBuildImages().keySet();
 * </pre>
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Component
public class BuildToolsProperties {

    @Autowired
    private ConfigurableEnvironment environment;

    /**
     * 构建镜像映射表
     *
     * <p>Key: 构建工具名称（如：android, gradle, maven, node等）</p>
     * <p>Value: 该构建工具对应的Docker镜像列表</p>
     */
    private final Map<String, List<String>> buildTools = new HashMap<>();

    /**
     * 初始化方法，动态从环境中加载所有构建工具配置
     */
    @PostConstruct
    public void init() {
        loadBuildToolsFromEnvironment();
        System.out.println("✅ BuildToolsProperties初始化完成，加载了 " + buildTools.size() + " 种构建工具");
    }

    /**
     * 动态从环境中加载构建工具配置
     */
    private void loadBuildToolsFromEnvironment() {
        // 正则表达式匹配 build.tools.{toolType}[{index}] 格式
        Pattern pattern = Pattern.compile("^build\\.tools\\.([^.\\[]+)\\[(\\d+)\\]$");

        // 遍历所有配置源
        for (PropertySource<?> propertySource : environment.getPropertySources()) {
            if (propertySource instanceof EnumerablePropertySource) {
                EnumerablePropertySource<?> enumerable = (EnumerablePropertySource<?>) propertySource;
                String[] propertyNames = enumerable.getPropertyNames();

                for (String propertyName : propertyNames) {
                    Matcher matcher = pattern.matcher(propertyName);
                    if (matcher.matches()) {
                        String toolType = matcher.group(1);
                        int index = Integer.parseInt(matcher.group(2));
                        String imageConfig = environment.getProperty(propertyName);

                        if (imageConfig != null) {
                            buildTools.computeIfAbsent(toolType, k -> new ArrayList<>());

                            // 确保列表足够大
                            List<String> toolImages = buildTools.get(toolType);
                            while (toolImages.size() <= index) {
                                toolImages.add(null);
                            }

                            // 设置对应索引的值
                            toolImages.set(index, imageConfig);
                        }
                    }
                }
            }
        }

        // 清理null值并排序
        buildTools.forEach((toolType, images) -> {
            images.removeIf(Objects::isNull);
        });

        System.out.println("🔧 动态加载配置完成，发现工具类型: " + buildTools.keySet());
    }

    /**
     * 根据工具类型获取对应的镜像列表
     *
     * @param toolType 构建工具类型（如：android, gradle, maven等）
     * @return 该工具对应的Docker镜像列表，如果工具不存在则返回空列表
     */
    public List<BuildToolBO> getImagesByToolType(String toolType) {
        if (buildTools == null){
            return Collections.emptyList();
        }
        List<String> images = buildTools.get(toolType);
        if (images == null) {
            return Collections.emptyList();
        }
        List<BuildToolBO> buildToolBOS = new ArrayList<>();
        for (int i = 0; i < images.size(); i++) {
            BuildToolBO buildToolBO = new BuildToolBO();
            String imageStr = images.get(i);
            String[] imageArr = imageStr.split("#");
            buildToolBO.setId(toolType + "-" + i);
            buildToolBO.setToolName(imageArr[0]);
            buildToolBO.setToolImage(imageArr[1]);
            buildToolBO.setType(toolType);
            buildToolBO.setArgs("");
            buildToolBOS.add(buildToolBO);
        }
        return buildToolBOS;
    }

    /**
     * 根据工具版本获取对应的镜像
     *
     * @param toolVersion 构建工具版本（如：gradle7.2, maven3.6.3等）
     * @return 该工具版本对应的Docker镜像，如果工具版本不存在则返回null
     */
    public BuildToolBO getImageByToolVersion(String toolType, String toolVersion) {
        List<BuildToolBO> imagesByToolType = getImagesByToolType(toolType);
        if (EmptyValidator.isEmpty(imagesByToolType)) {
            return null;
        }
        return imagesByToolType.stream().filter(each -> toolVersion.equals(each.getToolName())).findFirst().orElse(null);
    }

    /**
     * 检查是否包含指定的构建工具
     *
     * @param toolType 构建工具类型
     * @return 如果包含该工具返回true，否则返回false
     */
    public boolean containsTool(String toolType) {
        return buildTools != null && buildTools.containsKey(toolType);
    }

    /**
     * 获取所有支持的构建工具类型
     *
     * @return 所有构建工具类型的集合
     */
    public List<BuildToolBO> getAllBuildTools() {
        if (buildTools == null){
            return Collections.emptyList();
        }
        List<BuildToolBO> buildToolBOS = new ArrayList<>();
        buildTools.keySet().forEach(each -> {
            List<BuildToolBO> imagesByToolType = getImagesByToolType(each);
            if (EmptyValidator.isNotEmpty(imagesByToolType)) {
                buildToolBOS.addAll(imagesByToolType);
            }
        });
        return buildToolBOS;
    }

    @Override
    public String toString() {
        return "BuildToolsProperties{" +
                "buildTools=" + buildTools +
                '}';
    }
}
