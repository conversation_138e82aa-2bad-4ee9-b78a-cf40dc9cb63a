package com.cmcc.cmdevops.ci.service.business;

import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.bo.BuildEnvironmentBO;
import com.cmcc.cmdevops.ci.service.bo.BuildEnvironmentPermissionBO;
import com.cmcc.cmdevops.ci.service.bo.BuildPermissionBO;
import com.cmcc.cmdevops.ci.service.bo.BuildTaskBO;

import java.util.List;

/**
 * 权限业务接口
 */
public interface BuildEnvironmentPermissionBizService {

    void save(List<BuildEnvironmentPermissionBO> buildEnvironmentPermissionBOs);

    void delete(String id);

    List<BuildEnvironmentPermissionBO> list(BuildEnvironmentPermissionBO buildEnvironmentPermissionBO);
}
