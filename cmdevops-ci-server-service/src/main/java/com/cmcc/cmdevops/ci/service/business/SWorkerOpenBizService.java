package com.cmcc.cmdevops.ci.service.business;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.cmcc.cmdevops.ci.service.bo.Metadata;

import java.util.Map;

// 软工开发工具
public interface SWorkerOpenBizService {

    JSONArray getImageRepository(String tenantId, String spaceId);

    JSONArray getNormalRepository(String tenantId, String spaceId);

    JSONArray getImageArtifacts(String tenantId, String repoKey);

    JSONArray getNormalArtifacts(String tenantId, String repoKey);

    JSONArray getImageVersions(String tenantId, String repoKey, String artiName);

    JSONArray getNormalVersions(String tenantId, String repoKey, String artiName);

    String addMetadata(Metadata metadata);

    String normalRepositoryUrl(String tenantId, String repositoryName);

    String dockerRepositoryUrl(String tenantId, String repositoryName);

    String normalRepositoryAuth(String userId);

    String dockerRepositoryAuth(String userId);


    // 获取文件内容
    String getFileContent(String tenantId, String projectId, String userId, String filePath, String ref);

    // 查询分支列表

    JSONArray getBranchList(String tenantId, String projectId, String userId);

    // 查询仓库列表
    // 获取默认token 接口
    Map<String, String> privateToken(String tenantId, String userId);

    // 代码仓库列表
    JSONArray getCodeRepositoryList(String tenantId, String spaceId, String userId);

    JSONObject getCodeMetadata(String tenantId, String projectId);

    // 获取commit提交详情
    JSONObject getLastCommit(String tenantId, String projectId, String ref, String userId);

    // 获取最后一次commit
    JSONObject getCommitMsg(String tenantId, String projectId, String commitId, String userId);

    // 获取TAG列表
    JSONArray getTagList(String tenantId, String projectId, String userId);

    JSONArray getBuildTaksList(int pageNo, int pageSize);

    String startTask(String taskId);

    // 查询构建状态
    String queryStatus(String buildSnapshotId);

    String queryLog(String buildSnapshotId);

    // 查询构建产物
    JSONArray getProducts(String buildSnapshotId);

    JSONArray queryArtifactName(String artifactType, String taskId);

    JSONArray queryArtifact(String artifactType, String artifactName, String version, String taskId, String buildId);

    JSONArray getBuildVersions(String taskId);

    JSONArray getUserList(String tenantId, String spaceId);

    JSONObject getCodeRepoMetaData(String projectId,String tenantId);

    JSONArray queryAccelerationNode(String source, String tenantId);

    JSONArray querySpaceList(String tenantId);

    JSONArray queryProjectList(String tenantId);

    JSONArray queryProjectSpaceRel(String tenantId, String projectId);
}
