package com.cmcc.cmdevops.ci.service.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildEnvironmentPermissionAtomService;
import com.cmcc.cmdevops.ci.service.atom.BuildPermissionAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildEnvironmentPermissionBO;
import com.cmcc.cmdevops.ci.service.bo.BuildPermissionBO;
import com.cmcc.cmdevops.ci.service.bo.BuildTaskBO;
import com.cmcc.cmdevops.ci.service.business.BuildEnvironmentPermissionBizService;
import com.cmcc.cmdevops.ci.service.business.BuildPermissionBizService;
import com.cmcc.cmdevops.ci.service.business.BuildTaskBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.business.util.UserUtils;
import com.cmcc.cmdevops.ci.service.dao.BuildEnvironmentPermissionDO;
import com.cmcc.cmdevops.ci.service.dao.BuildPermissionDO;
import com.cmcc.cmdevops.util.BeanCloner;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 构建环境业务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BuildEnvironmentPermissionBizServiceImpl implements BuildEnvironmentPermissionBizService {

    @Resource
    private BuildEnvironmentPermissionAtomService buildEnvironmentPermissionAtomService;


    @Override
    public void save(List<BuildEnvironmentPermissionBO> buildEnvironmentPermissionBOs) {
        for (BuildEnvironmentPermissionBO buildEnvironmentPermissionBO : buildEnvironmentPermissionBOs) {
            BuildEnvironmentPermissionDO buildEnvironmentPermissionDO = BeanCloner.clone(buildEnvironmentPermissionBO, BuildEnvironmentPermissionDO.class);
            buildEnvironmentPermissionDO.setTenantId(UserUtils.getTenantId());
            buildEnvironmentPermissionDO.setCreateUid(UserUtils.getUserId());
            buildEnvironmentPermissionAtomService.save(buildEnvironmentPermissionDO);
        }
    }

    @Override
    public void delete(String id) {
        buildEnvironmentPermissionAtomService.removeById(id);
    }

    @Override
    public List<BuildEnvironmentPermissionBO> list(BuildEnvironmentPermissionBO buildEnvironmentPermissionBO) {
        LambdaQueryWrapper<BuildEnvironmentPermissionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(BuildEnvironmentPermissionDO::getCreateTime);
        queryWrapper.eq(BuildEnvironmentPermissionDO::getEnvironmentId, buildEnvironmentPermissionBO.getEnvironmentId());
        queryWrapper.eq(BuildEnvironmentPermissionDO::getType, buildEnvironmentPermissionBO.getType());
        List<BuildEnvironmentPermissionDO> list = buildEnvironmentPermissionAtomService.list(queryWrapper);
        return BeanCloner.clone(list, BuildEnvironmentPermissionBO.class);
    }
}
