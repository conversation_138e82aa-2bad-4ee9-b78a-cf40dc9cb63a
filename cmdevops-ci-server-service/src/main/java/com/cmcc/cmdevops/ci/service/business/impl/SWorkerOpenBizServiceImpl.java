package com.cmcc.cmdevops.ci.service.business.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.common.packagescan.resource.ClassPathResource;
import com.cmcc.cmdevops.BaseResponse;
import com.cmcc.cmdevops.ci.service.bo.Metadata;
import com.cmcc.cmdevops.ci.service.business.SWorkerOpenBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.business.util.UserUtils;
import com.cmcc.cmdevops.exception.BusinessException;
import com.cmcc.cmdevops.open.sdk.ApiGwHttpMethod;
import com.cmcc.cmdevops.open.sdk.ApiGwHttpStatus;
import com.cmcc.cmdevops.open.sdk.ApiGwResult;
import com.cmcc.cmdevops.open.sdk.util.ApiGwSerializationUtil;
import com.cmcc.cmdevops.open.sdk.util.ApiGwUriUtil;
import com.cmcc.cmdevops.open.sdk.v1.ApiGwClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Service
public class SWorkerOpenBizServiceImpl implements SWorkerOpenBizService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SWorkerOpenBizServiceImpl.class);
    @Value("${ci.external-api.dev}")
    private Boolean isDev;

    private final static String X_TENANT_ID = "X-Tenant-Id";

    @Override
    public JSONObject getLastCommit(String tenantId, String projectId, String ref, String userId) {
        if (isDev) {
            String msg = """
                    {
                      "success": true,
                      "code": "",
                      "message": "",
                      "data": {
                        "projectName": "",
                        "projectPath": "",
                        "id": "fffffffffffffewjfklwjlkfjklwfjlkjwlkfjw",
                        "shortId": "jlkjwlkfjw",
                        "createdDate": "**********",
                        "authorName": "licdwfewf",
                        "message": "demotesttest",
                        "authoredDate": "",
                        "title": "testestset"
                      }
                    }
                    """;

            return JSONObject.parseObject(msg).getJSONObject("data");

        } else {
            String uri = ApiGwUriUtil.toUri("/cmdevops-code/server/openapi/commit/projects/" + projectId + "/last-commit?ref=" + ref + "&userAccount=" + userId);
            ApiGwResult<BaseResponse> result = null;
            Map<String, String> headers = new HashMap<>();
            headers.put(X_TENANT_ID, tenantId);

            try {
                result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, BaseResponse.class);
            } catch (Exception e) {
                throw new BusinessException("获取最新提交记录异常");
            }

            if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
                System.out.println("响应数据：" + result.getBody());
                return JSON.parseObject(JSON.toJSONString(result.getBody())).getJSONObject("data");
            } else {
                System.err.println("调用失败!错误码:" + result.getStatus());
                return null;
            }
        }
    }

    @Override
    public JSONObject getCommitMsg(String tenantId, String projectId, String commitId, String userId) {
        if (isDev) {
            String msg = """
                    {
                      "success": true,
                      "code": "",
                      "message": "",
                      "data": {
                        "projectName": "",
                        "projectPath": "",
                        "id": "fffffffffffffewjfklwjlkfjklwfjlkjwlkfjw",
                        "shortId": "jlkjwlkfjw",
                        "createdDate": "**********",
                        "authorName": "licdwfewf",
                        "message": "demotesttest",
                        "authoredDate": "",
                        "title": "testestset"
                      }
                    }
                    """;

            return JSONObject.parseObject(msg).getJSONObject("data");

        }
        String uri = ApiGwUriUtil.toUri("/cmdevops-code/server/openapi/commit/projects/" + projectId + "/commit/" + commitId + "?userAccount=" + userId);
        ApiGwResult<BaseResponse> result = null;
        Map<String, String> headers = new HashMap<>();
        headers.put(X_TENANT_ID, tenantId);

        try {
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, BaseResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("获取指定提交记录异常");
        }

        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            System.out.println("响应数据：" + result.getBody());
            return JSON.parseObject(JSON.toJSONString(result.getBody())).getJSONObject("data");
        } else {
            System.err.println("调用失败!错误码:" + result.getStatus());
            return null;
        }
    }

    @Override
    public JSONArray getTagList(String tenantId, String projectId, String userId) {
        if (isDev) {
            String json = """
                    {
                      "success": true,
                      "code": "",
                      "message": "",
                      "data": [
                        {
                          "name": "tag1",
                          "tagMessage": "",
                          "isProtected": true,
                          "createBaseCommitId": "1111",
                          "createBaseCommitMessage": "",
                          "createTime": "yyyy-MM-dd HH:mm:ss",
                          "creator": ""
                        },
                        {
                          "name": "tag2",
                          "tagMessage": "",
                          "isProtected": true,
                          "createBaseCommitId": "22222",
                          "createBaseCommitMessage": "",
                          "createTime": "yyyy-MM-dd HH:mm:ss",
                          "creator": ""
                        }
                      ],
                      "pageNo": 0,
                      "pageSize": 0,
                      "count": 0,
                      "pageCount": 0
                    }""";
            return JSONObject.parseObject(json).getJSONArray("data");
        }
        String uri = ApiGwUriUtil.toUri("/cmdevops-code/server/openapi/tag/projects/" + projectId + "/list?pageNo=1&pageSize=9999&userAccount=" + userId);
        ApiGwResult<JSONObject> result = null;
        Map<String, String> headers = new HashMap<>();
        headers.put(X_TENANT_ID, tenantId);

        try {
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, JSONObject.class);
            if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
                LOGGER.info("响应数据：" + result.getBody());
                JSONObject body = JSON.parseObject(JSON.toJSONString(result.getBody()));
                if (body.getBoolean("success")) {
                    return body.getJSONArray("data");
                } else {
                    LOGGER.error("调用失败!错误信息:" + body.getString("message"));
                    return null;
                }

            } else {
                LOGGER.error("调用失败!错误码:" + result.getStatus());
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("获取代码仓库TAG列表异常");
        }
    }


    @Override
    public JSONArray getBuildTaksList(int pageNo, int pageSize) {
        String uri = ApiGwUriUtil.toUri("/cmdevops-ci/server/openapi/build/pageList");
        ApiGwResult<JSONObject> result = null;
        Map<String, String> headers = new HashMap<>();
        Map<String, Object> map = new HashMap<>();
        map.put("pageNo", 1);
        map.put("pageSize", 10);
//        map.put("buildTaskDTO", "");
        try {
            String params = ApiGwSerializationUtil.toJson(map);
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.POST, headers, params, JSONObject.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            LOGGER.info("响应数据：" + result.getBody());
            JSONObject body = JSON.parseObject(JSON.toJSONString(result.getBody()));
            if (body.getBoolean("success")) {
                return body.getJSONArray("data");
            } else {
                LOGGER.error("调用失败!错误信息:" + body.getString("message"));
                return null;
            }

        } else {
            LOGGER.error("调用失败!错误码:" + result.getStatus());
            return null;
        }

    }

    @Override
    public String startTask(String taskId) {
        String uri = ApiGwUriUtil.toUri("/cmdevops-ci/server/openapi/build/start/" + taskId);
        ApiGwResult<JSONObject> result = null;
        Map<String, String> headers = new HashMap<>();
        Map<String, Object> map = new HashMap<>();

//        map.put("buildTaskDTO", "");
        try {
            String params = ApiGwSerializationUtil.toJson(map);
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.POST, headers, params, JSONObject.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            LOGGER.info("响应数据：" + result.getBody());
            JSONObject body = JSON.parseObject(JSON.toJSONString(result.getBody()));
            if (body.getBoolean("success")) {
                return body.getString("data");
            } else {
                LOGGER.error("调用失败!错误信息:" + body.getString("message"));
                return null;
            }

        } else {
            LOGGER.error("调用失败!错误码:" + result.getStatus());
            return null;
        }

    }

    @Override
    public String queryStatus(String buildSnapshotId) {
        String uri = ApiGwUriUtil.toUri("/cmdevops-ci/server/openapi/build/queryStatus/" + buildSnapshotId);
        ApiGwResult<JSONObject> result = null;
        Map<String, String> headers = new HashMap<>();
        Map<String, Object> map = new HashMap<>();

//        map.put("buildTaskDTO", "");
        try {
            String params = ApiGwSerializationUtil.toJson(map);
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, JSONObject.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            LOGGER.info("响应数据：" + result.getBody());
            JSONObject body = JSON.parseObject(JSON.toJSONString(result.getBody()));
            if (body.getBoolean("success")) {
                return body.getString("data");
            } else {
                LOGGER.error("调用失败!错误信息:" + body.getString("message"));
                return null;
            }

        } else {
            LOGGER.error("调用失败!错误码:" + result.getStatus());
            return null;
        }
    }

    @Override
    public String queryLog(String buildSnapshotId) {
        String uri = ApiGwUriUtil.toUri("/cmdevops-ci/server/openapi/build/queryLog/" + buildSnapshotId);
        ApiGwResult<JSONObject> result = null;
        Map<String, String> headers = new HashMap<>();
        Map<String, Object> map = new HashMap<>();

//        map.put("buildTaskDTO", "");
        try {
            String params = ApiGwSerializationUtil.toJson(map);
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, JSONObject.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            LOGGER.info("响应数据：" + result.getBody());
            JSONObject body = JSON.parseObject(JSON.toJSONString(result.getBody()));
            if (body.getBoolean("success")) {
                return body.getString("data");
            } else {
                LOGGER.error("调用失败!错误信息:" + body.getString("message"));
                return null;
            }

        } else {
            LOGGER.error("调用失败!错误码:" + result.getStatus());
            return null;
        }
    }

    @Override
    public JSONArray getProducts(String buildSnapshotId) {
        String uri = ApiGwUriUtil.toUri("/cmdevops-ci/server/openapi/build/products/" + buildSnapshotId);
        ApiGwResult<JSONObject> result = null;
        Map<String, String> headers = new HashMap<>();
        Map<String, Object> map = new HashMap<>();
        map.put("pageNo", 1);
        map.put("pageSize", 10);
//        map.put("buildTaskDTO", "");
        try {
            String params = ApiGwSerializationUtil.toJson(map);
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, JSONObject.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            LOGGER.info("响应数据：" + result.getBody());
            JSONObject body = JSON.parseObject(JSON.toJSONString(result.getBody()));
            if (body.getBoolean("success")) {
                return body.getJSONArray("data");
            } else {
                LOGGER.error("调用失败!错误信息:" + body.getString("message"));
                return null;
            }

        } else {
            LOGGER.error("调用失败!错误码:" + result.getStatus());
            return null;
        }
    }

    @Override
    public JSONArray queryArtifactName(String artifactType, String taskId) {
        String uri = ApiGwUriUtil.toUri("/cmdevops-ci/server/openapi/build/queryArtifactName?artifactType" + (EmptyValidator.isEmpty(artifactType) ? "" : artifactType) + "&taskId=" + (EmptyValidator.isEmpty(taskId) ? "" : taskId));
        ApiGwResult<JSONObject> result = null;
        Map<String, String> headers = new HashMap<>();
        Map<String, Object> map = new HashMap<>();
        map.put("pageNo", 1);
        map.put("pageSize", 10);
//        map.put("buildTaskDTO", "");
        try {
            String params = ApiGwSerializationUtil.toJson(map);
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, JSONObject.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            LOGGER.info("响应数据：" + result.getBody());
            JSONObject body = JSON.parseObject(JSON.toJSONString(result.getBody()));
            if (body.getBoolean("success")) {
                return body.getJSONArray("data");
            } else {
                LOGGER.error("调用失败!错误信息:" + body.getString("message"));
                return null;
            }

        } else {
            LOGGER.error("调用失败!错误码:" + result.getStatus());
            return null;
        }
    }

    @Override
    public JSONArray queryArtifact(String artifactType, String artifactName, String version, String taskId, String buildId) {
        String uri = ApiGwUriUtil.toUri("/cmdevops-ci/server/openapi/build/queryArtifact?artifactType" + (EmptyValidator.isEmpty(artifactType) ? "" : artifactType) + "&taskId=" + (EmptyValidator.isEmpty(taskId) ? "" : taskId)
                + "&version=" + (EmptyValidator.isEmpty(version) ? "" : version) + "&buildId=" + (EmptyValidator.isEmpty(buildId) ? "" : buildId) + "&buildId=" + (EmptyValidator.isEmpty(artifactName) ? "" : artifactName)
        );
        ApiGwResult<JSONObject> result = null;
        Map<String, String> headers = new HashMap<>();
        Map<String, Object> map = new HashMap<>();
        map.put("pageNo", 1);
        map.put("pageSize", 10);
//        map.put("buildTaskDTO", "");
        try {
            String params = ApiGwSerializationUtil.toJson(map);
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, JSONObject.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            LOGGER.info("响应数据：" + result.getBody());
            JSONObject body = JSON.parseObject(JSON.toJSONString(result.getBody()));
            if (body.getBoolean("success")) {
                return body.getJSONArray("data");
            } else {
                LOGGER.error("调用失败!错误信息:" + body.getString("message"));
                return null;
            }

        } else {
            LOGGER.error("调用失败!错误码:" + result.getStatus());
            return null;
        }
    }

    @Override
    public JSONArray getBuildVersions(String taskId) {
        String uri = ApiGwUriUtil.toUri("/cmdevops-ci/server/openapi/build/versions/" + taskId);
        ApiGwResult<JSONObject> result = null;
        Map<String, String> headers = new HashMap<>();
        Map<String, Object> map = new HashMap<>();
        map.put("pageNo", 1);
        map.put("pageSize", 10);
//        map.put("buildTaskDTO", "");
        try {
            String params = ApiGwSerializationUtil.toJson(map);
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, JSONObject.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            LOGGER.info("响应数据：" + result.getBody());
            JSONObject body = JSON.parseObject(JSON.toJSONString(result.getBody()));
            if (body.getBoolean("success")) {
                return body.getJSONArray("data");
            } else {
                LOGGER.error("调用失败!错误信息:" + body.getString("message"));
                return null;
            }

        } else {
            LOGGER.error("调用失败!错误码:" + result.getStatus());
            return null;
        }
    }

    private JSONArray getRepository(String tenantId, String spaceId, String repoType) {
        String userId = UserUtils.getUserId();
        String uri = ApiGwUriUtil.toUri("/cmdevops-repo/artifactory/openapi/repo/getRepos?tenantId=" + tenantId + "&space111Code=" + spaceId + "&repoType=" + repoType);
        ApiGwResult<BaseResponse> result = null;
        Map<String, String> headers = new HashMap<>();
        headers.put(X_TENANT_ID, tenantId);
        Map<String, Object> map = new HashMap<>();

        try {
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, BaseResponse.class);
        } catch (Exception e) {
            throw new BusinessException("获取仓库列表异常");
        }

        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            LOGGER.info("响应数据：" + result.getBody());
            JSONObject body = JSON.parseObject(JSON.toJSONString(result.getBody()));
            if (body.getBoolean("success")) {
                return body.getJSONArray("data");
            } else {
                LOGGER.error("调用失败!错误信息:" + body.getString("message"));
                return null;
            }

        } else {
            LOGGER.error("调用失败!错误码:" + result.getStatus());
            return null;
        }


    }

    @Override
    public JSONArray getImageRepository(String tenantId, String spaceId) {
        if (isDev) {
            String json = """
                    {
                      "success": true,
                      "code": "success",
                      "message": "仓库列表查询成功",
                      "causedBy": "",
                      "data": [
                        {
                          "id": 1,
                          "showRepoKey":"devops-test",
                          "repoKey": "devops-test",
                          "repoType": "Docker",
                          "accessLevel": 1,
                          "descInfo": "示例仓库",
                          "tenantId": 101,
                          "systemId": 202,
                          "spaceId": 303,
                          "createTime": "2024-05-01 12:00:00",
                          "createUid": "USER73280656412",
                          "createUsername": "管理员",
                          "updateTime": "2024-05-10 12:00:00",
                          "updateUid": "USER73280656412",
                          "updateUsername": "管理员",
                          "deleted": false,
                          "deleteTime": null
                        },
                        {
                          "id": 1,
                          "showRepoKey":"devops",
                          "repoKey": "devops",  
                          "repoType": "Docker",
                          "accessLevel": 1,
                          "descInfo": "示例仓库",
                          "tenantId": 101,
                          "systemId": 202,
                          "spaceId": 303,
                          "createTime": "2024-05-01 12:00:00",
                          "createUid": "USER73280656412",
                          "createUsername": "管理员",
                          "updateTime": "2024-05-10 12:00:00",
                          "updateUid": "USER73280656412",
                          "updateUsername": "管理员",
                          "deleted": false,
                          "deleteTime": null
                        }
                      ]}""";
            return JSONObject.parseObject(json).getJSONArray("data");
        }
        return getRepository(tenantId, spaceId, "docker");
    }

    @Override
    public JSONArray getNormalRepository(String tenantId, String spaceId) {
        if (isDev) {
            String json = """
                    {
                      "success": true,
                      "code": "200",
                      "message": "成功",
                      "data": [
                        {
                          "id": 1,
                          "showRepoKey":"sigong-demo1",
                          "repoKey": "bj-maven-0604",
                          "artiName": "my-artifact",
                          "latestVersion": "1.0.0",
                          "artiPath": "bj-maven-0604/my-artifact",
                          "artiType": 1,
                          "artiSource": 0,
                          "sha256": "abc123...",
                          "sha1": "def456...",
                          "md5": "ghi789...",
                          "createTime": "2024-06-01 10:00:00",
                          "createUid": "USER73280656412",
                          "createUsername": "张三",
                          "updateTime": "2024-06-02 11:00:00",
                          "updateUid":  "USER73280656413",
                          "updateUsername": "李四",
                          "deleted": false
                        },
                        {
                          "id": 1,
                          "showRepoKey":"sigong-demo2",
                          "repoKey": "bj-maven-0604",
                          "artiName": "my-artifact",
                          "latestVersion": "1.0.0",
                          "artiPath": "bj-maven-0604/my-artifact",
                          "artiType": 1,
                          "artiSource": 0,
                          "sha256": "abc123...",
                          "sha1": "def456...",
                          "md5": "ghi789...",
                          "createTime": "2024-06-01 10:00:00",
                          "createUid": "USER73280656412",
                          "createUsername": "张三",
                          "updateTime": "2024-06-02 11:00:00",
                          "updateUid":  "USER73280656413",
                          "updateUsername": "李四",
                          "deleted": false
                        }
                      ],
                      "pageNo": 1,
                      "pageSize": 10,
                      "count": 50,
                      "pageCount": 5
                    }""";
            return JSONObject.parseObject(json).getJSONArray("data");
        }
        return getRepository(tenantId, spaceId, "generic");
    }

    @Override
    public JSONArray getImageArtifacts(String tenantId, String repoKey) {
        if (isDev) {
            String json = """
                    {
                      "success": true,
                      "code": "200",
                      "message": "成功",
                      "data": [
                        {
                          "id": 1,
                          "repoKey": "bj-maven-0604",
                          "artiName": "ant",
                          "latestVersion": "1.0.0",
                          "artiPath": "bj-maven-0604/my-artifact",
                          "artiType": 1,
                          "artiSource": 0,
                          "sha256": "abc123...",
                          "sha1": "def456...",
                          "md5": "ghi789...",
                          "createTime": "2024-06-01 10:00:00",
                          "createUid": "USER73280656412",
                          "createUsername": "张三",
                          "updateTime": "2024-06-02 11:00:00",
                          "updateUid":  "USER73280656413",
                          "updateUsername": "李四",
                          "deleted": false
                        },
                        {
                          "id": 1,
                          "repoKey": "bj-maven-0604",
                          "artiName": "my-artifact",
                          "latestVersion": "1.0.0",
                          "artiPath": "bj-maven-0604/my-artifact",
                          "artiType": 1,
                          "artiSource": 0,
                          "sha256": "abc123...",
                          "sha1": "def456...",
                          "md5": "ghi789...",
                          "createTime": "2024-06-01 10:00:00",
                          "createUid": "USER73280656412",
                          "createUsername": "张三",
                          "updateTime": "2024-06-02 11:00:00",
                          "updateUid":  "USER73280656413",
                          "updateUsername": "李四",
                          "deleted": false
                        }
                      ],
                      "pageNo": 1,
                      "pageSize": 10,
                      "count": 50,
                      "pageCount": 5
                    }""";
            return JSONObject.parseObject(json).getJSONArray("data");
        }
        return getArtifacts(tenantId, repoKey, "docker");
    }

    @Override
    public JSONArray getNormalArtifacts(String tenantId, String repoKey) {
        if (isDev) {
            String json = """
                    {
                      "success": true,
                      "code": "200",
                      "message": "成功",
                      "data": [
                        {
                          "id": 1,
                          "repoKey": "bj-maven-0604",
                          "artiName": "my-artifact",
                          "latestVersion": "1.0.0",
                          "artiPath": "bj-maven-0604/my-artifact",
                          "artiType": 1,
                          "artiSource": 0,
                          "sha256": "abc123...",
                          "sha1": "def456...",
                          "md5": "ghi789...",
                          "createTime": "2024-06-01 10:00:00",
                          "createUid": "USER73280656412",
                          "createUsername": "张三",
                          "updateTime": "2024-06-02 11:00:00",
                          "updateUid":  "USER73280656413",
                          "updateUsername": "李四",
                          "deleted": false
                        },
                        {
                          "id": 1,
                          "repoKey": "bj-maven-0604",
                          "artiName": "my-artifact",
                          "latestVersion": "1.0.0",
                          "artiPath": "bj-maven-0604/my-artifact",
                          "artiType": 1,
                          "artiSource": 0,
                          "sha256": "abc123...",
                          "sha1": "def456...",
                          "md5": "ghi789...",
                          "createTime": "2024-06-01 10:00:00",
                          "createUid": "USER73280656412",
                          "createUsername": "张三",
                          "updateTime": "2024-06-02 11:00:00",
                          "updateUid":  "USER73280656413",
                          "updateUsername": "李四",
                          "deleted": false
                        }
                      ],
                      "pageNo": 1,
                      "pageSize": 10,
                      "count": 50,
                      "pageCount": 5
                    }""";
            return JSONObject.parseObject(json).getJSONArray("data");
        }
        return getArtifacts(tenantId, repoKey, "generic");
    }

    private JSONArray getArtifacts(String tenantId, String repoKey, String repoType) {
        String uri = ApiGwUriUtil.toUri("/cmdevops-repo/artifactory/openapi/artifact/getArtifacts?repoKey=" + repoKey + "&repoType=" + repoType + "&pageNo=1&pageSize=10");
        ApiGwResult<JSONObject> result = null;
        Map<String, String> headers = new HashMap<>();
        headers.put(X_TENANT_ID, tenantId);

        try {
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, JSONObject.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            LOGGER.info("响应数据：" + result.getBody());
            JSONObject body = JSON.parseObject(JSON.toJSONString(result.getBody()));
            if (body.getBoolean("success")) {
                return body.getJSONArray("data");
            } else {
                LOGGER.error("调用失败!错误信息:" + body.getString("message"));
                return null;
            }

        } else {
            LOGGER.error("调用失败!错误码:" + result.getStatus());
            return null;
        }


    }

    @Override
    public JSONArray getImageVersions(String tenantId, String repoKey, String artiName) {
        if (isDev) {
            String json = """
                    {
                      "success": true,
                      "code": "200",
                      "message": "成功",
                      "data": [
                        {
                          "id": 1,
                          "repoKey": "maven-central",
                          "artiName": "my-artifact",
                          "artiVersion": "1.10-jdk8",
                          "artiPath": "/com/example/my-artifact/1.0.0",
                          "artiType": 1,
                          "artiSource": 0,
                          "sha256": "abc123...",
                          "sha1": "def456...",
                          "md5": "ghi789...",
                          "createTime": "2024-06-01 10:00:00",
                          "createUid": "USER73280656412",
                          "createUsername": "张三",
                          "updateTime": "2024-06-02 11:00:00",
                          "updateUid": "USER73280656412",
                          "updateUsername": "李四",
                          "deleted": false
                        },
                        {
                          "id": 1,
                          "repoKey": "maven-central",
                          "artiName": "my-artifact",
                          "artiVersion": "1.9.14-jdk7",
                          "artiPath": "/com/example/my-artifact/1.0.0",
                          "artiType": 1,
                          "artiSource": 0,
                          "sha256": "abc123...",
                          "sha1": "def456...",
                          "md5": "ghi789...",
                          "createTime": "2024-06-01 10:00:00",
                          "createUid": "USER73280656412",
                          "createUsername": "张三",
                          "updateTime": "2024-06-02 11:00:00",
                          "updateUid": "USER73280656412",
                          "updateUsername": "李四",
                          "deleted": false
                        }
                      ]
                    }""";
            return JSONObject.parseObject(json).getJSONArray("data");
        }
        String uri = ApiGwUriUtil.toUri("/cmdevops-repo/artifactory/openapi/version/getVersions?repoKey=" + repoKey + "&artiName=" + artiName);
        ApiGwResult<BaseResponse> result = null;
        Map<String, String> headers = new HashMap<>();
        headers.put(X_TENANT_ID, tenantId);
        try {
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, BaseResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("获取制品版本异常");
        }

        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            LOGGER.info("响应数据：" + result.getBody());
            JSONObject body = JSON.parseObject(JSON.toJSONString(result.getBody()));
            if (body.getBoolean("success")) {
                return body.getJSONArray("data");
            } else {
                LOGGER.error("调用失败!错误信息:" + body.getString("message"));
                return null;
            }

        } else {
            LOGGER.error("调用失败!错误码:" + result.getStatus());
            return null;
        }

    }

    @Override
    public JSONArray getNormalVersions(String tenantId, String repoKey, String artiName) {
        if (isDev) {
            String json = """
                    {
                      "success": true,
                      "code": "200",
                      "message": "成功",
                      "data": [
                        {
                          "id": 1,
                          "repoKey": "maven-central",
                          "artiName": "my-artifact",
                          "artiVersion": "1.0.0",
                          "artiPath": "/com/example/my-artifact/1.0.0",
                          "artiType": 1,
                          "artiSource": 0,
                          "sha256": "abc123...",
                          "sha1": "def456...",
                          "md5": "ghi789...",
                          "createTime": "2024-06-01 10:00:00",
                          "createUid": "USER73280656412",
                          "createUsername": "张三",
                          "updateTime": "2024-06-02 11:00:00",
                          "updateUid": "USER73280656412",
                          "updateUsername": "李四",
                          "deleted": false
                        },
                        {
                          "id": 1,
                          "repoKey": "maven-central",
                          "artiName": "my-artifact",
                          "artiVersion": "2.0.0",
                          "artiPath": "/com/example/my-artifact/1.0.0",
                          "artiType": 1,
                          "artiSource": 0,
                          "sha256": "abc123...",
                          "sha1": "def456...",
                          "md5": "ghi789...",
                          "createTime": "2024-06-01 10:00:00",
                          "createUid": "USER73280656412",
                          "createUsername": "张三",
                          "updateTime": "2024-06-02 11:00:00",
                          "updateUid": "USER73280656412",
                          "updateUsername": "李四",
                          "deleted": false
                        }
                      ]
                    }""";
            return JSONObject.parseObject(json).getJSONArray("data");
        }
        return getImageVersions(tenantId, repoKey, artiName);
    }

    @Override
    public String addMetadata(Metadata metadata) {
        String uri = ApiGwUriUtil.toUri("/cmdevops-repo/artifactory/openapi/addMetadata");
        ApiGwResult<BaseResponse> result = null;
        try {
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.POST, null, JSONObject.toJSONString(metadata), BaseResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("推送元数据异常");
        }
        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            LOGGER.info("响应数据：" + result.getBody());
        } else {
            LOGGER.error("调用失败!错误码:" + result.getStatus());
            LOGGER.error("调用失败!错误结果:" + result.getBody());
        }
        return "";
    }

    @Override
    public String normalRepositoryUrl(String tenantId, String repositoryName) {
        if (isDev) {
            // mock数据
            String json = """
                    {
                      "success": true,
                      "code": "SUCCESS",
                      "message": "操作成功",
                      "data": {
                        "artiDomainPrivate": "http://60.205.112.114:8082/artifactory",
                        "imageDomainPublic": "60.205.112.114:8082",
                        "artiType": 1,
                        "artiDomainPublic": "http://10.88.202.177:8082/artifactory",
                        "imageType": 1,
                        "imageDomainPrivate": "60.205.112.114:8082"
                      }
                    }""";
            return JSONObject.parseObject(json).getJSONObject("data").getString("artiDomainPublic");

        }
        return getRepositoryUrl(tenantId, repositoryName, "normal");
    }

    @Override
    public String dockerRepositoryUrl(String tenantId, String repositoryName) {
        if (isDev) {
            String json = """
                    {
                      "success": true,
                      "code": "SUCCESS",
                      "message": "操作成功",
                      "data": {
                        "artiDomainPrivate": "http://60.205.112.114:8082/artifactory",
                        "imageDomainPublic": "10.86.12.11:20200",
                        "artiType": 1,
                        "artiDomainPublic": "http://10.88.202.177:8082/artifactory",
                        "imageType": 1,
                        "imageDomainPrivate": "60.205.112.114:8082"
                      }
                    }""";
            return JSONObject.parseObject(json).getJSONObject("data").getString("imageDomainPublic");

        }
        return getRepositoryUrl(tenantId, repositoryName, "docker");
    }

    private String getRepositoryUrl(String tenantId, String repositoryName, String type) {

        String uri = ApiGwUriUtil.toUri("/cmdevops-repo/artifactory/openapi/config/getDomainNames");
        ApiGwResult<BaseResponse> result = null;
        Map<String, String> headers = new HashMap<>();
        headers.put(X_TENANT_ID, tenantId);

        try {
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, BaseResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("获取仓库getDomainNames异常");
        }

        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            LOGGER.info("响应数据：" + result.getBody());
            JSONObject body = JSON.parseObject(JSON.toJSONString(result.getBody()));
            JSONObject data = body.getJSONObject("data");
            if ("docker".equals(type)) {
                return data.getString("imageDomainPublic");
            } else if ("normal".equals(type)) {
                return data.getString("artiDomainPublic");
            } else {
                return null;
            }

        } else {
            LOGGER.error("调用失败!错误码:" + result.getStatus());
            return null;
        }

    }

    @Override
    public String normalRepositoryAuth(String userAccount) {
        if (isDev) {
            String json = """
                    {
                        "success": true,
                        "code": "success",
                        "message": "获取用户制品库令牌成功",
                        "data": {
                            "userKey": "admin",
                            "userToken": "qazwsxedc123"
                        }
                    }""";
            return JSONObject.parseObject(json).getJSONObject("data").getString("userKey") + "#######" +
                    JSONObject.parseObject(json).getJSONObject("data").getString("userToken");

        }
        return getRepositoryAuth(userAccount, "generic");
    }

    @Override
    public String dockerRepositoryAuth(String userAccount) {
        if (isDev) {
            String json = """
                    {
                        "success": true,
                        "code": "success",
                        "message": "获取用户制品库令牌成功",
                        "data": {
                            "userKey": "licf_csd",
                            "userToken": "Docker.2022!"
                        }
                    }""";
            return JSONObject.parseObject(json).getJSONObject("data").getString("userKey") + "#######" +
                    JSONObject.parseObject(json).getJSONObject("data").getString("userToken");

        }
        return getRepositoryAuth(userAccount, "docker");
    }

    private String getRepositoryAuth(String userAccount, String repoType) {
        String uri = ApiGwUriUtil.toUri("/cmdevops-repo/artifactory/openapi/user/getToken" + (userAccount != null ? "?userId=" + userAccount : "") + (repoType != null ? "&repoType=" + repoType : ""));
        ApiGwResult<BaseResponse> result = null;
        try {
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, null, null, BaseResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("获取个人仓库认证异常");
        }


        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            System.out.println("响应数据：" + result.getBody());
            JSONObject data = JSON.parseObject(JSON.toJSONString(result.getBody())).getJSONObject("data");
            return data.getString("userKey") + "#######" + data.getString("userToken");
        } else {
            System.err.println("调用失败!错误码:" + result.getStatus());
            return null;
        }
    }

    @Override
    public String getFileContent(String tenantId, String projectId, String userId, String filePath, String ref) {
        if (isDev) {
            String json = """
                    {
                      "success": true,
                      "code": "",
                      "message": "",
                      "data": {
                        "fileName": "",
                        "content": "脚本",
                        "size": 0,
                        "encode": "text",
                        "lastCommitId": ""
                      }
                    }""";
            return JSONObject.parseObject(json).getJSONObject("data").getString("content");
        }
        String uri = ApiGwUriUtil.toUri("/cmdevops-code/server/openapi/file/projects/" + projectId + "/content?ref=" + ref + "&userAccount=" + userId + "&path=" + filePath);
        ApiGwResult<BaseResponse> result = null;
        Map<String, String> headers = new HashMap<>();
        headers.put(X_TENANT_ID, tenantId);

        try {
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, BaseResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            System.out.println("响应数据：" + result.getBody());
            return JSON.parseObject(JSON.toJSONString(result.getBody())).getJSONObject("data").getString("content");
        } else {
            System.err.println("调用失败!错误码:" + result.getStatus());
            return null;
        }
    }

    @Override
    public JSONArray getBranchList(String tenantId, String projectId, String userId) {
        if (isDev) {
            String json = """
                    {
                      "success": true,
                      "code": "",
                      "message": "",
                      "data": [
                        {
                          "name": "master",
                          "isProtected": true,
                          "isDefault": true,
                          "lastCommitId": "",
                          "lastCommitMessage": "",
                          "lastCommitAuthor": "",
                          "behind": 0,
                          "ahead": 0
                        },
                        {
                          "name": "dev",
                          "isProtected": true,
                          "isDefault": true,
                          "lastCommitId": "",
                          "lastCommitMessage": "",
                          "lastCommitAuthor": "",
                          "behind": 0,
                          "ahead": 0
                        },
                        {
                          "name": "main",
                          "isProtected": true,
                          "isDefault": true,
                          "lastCommitId": "",
                          "lastCommitMessage": "",
                          "lastCommitAuthor": "",
                          "behind": 0,
                          "ahead": 0
                        }
                      ],
                      "pageNo": 0,
                      "pageSize": 0,
                      "count": 0,
                      "pageCount": 0
                    }""";
            return JSONObject.parseObject(json).getJSONArray("data");
        }
        String uri = ApiGwUriUtil.toUri("/cmdevops-code/server/openapi/branch/projects/" + projectId + "/list?pageNo=1&pageSize=9999&userAccount=" + userId);
        ApiGwResult<JSONObject> result = null;
        Map<String, String> headers = new HashMap<>();
        headers.put(X_TENANT_ID, tenantId);

        try {
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, JSONObject.class);
            if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
                System.out.println("响应数据：" + result.getBody());
                return JSON.parseObject(JSON.toJSONString(result.getBody())).getJSONArray("data");
            } else {
                System.err.println("调用失败!错误码:" + result.getStatus());
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("获取代码分支异常");
        }
    }

    @Override
    public Map<String, String> privateToken(String tenantId, String userId) {
        if (isDev) {
            String json = """
                    {
                      "success": true,
                      "code": "",
                      "message": "",
                      "data": {
                          "id": 0,
                          "userId": "licf_csd",
                          "accessToken": "X2Sznz8zd-4xb91ZUvMD",
                          "name": "",
                          "createTime": "yyyy-MM-dd HH:mm:ss",
                          "gitAccessTokenId": 0,
                          "expiresAt": "yyyy-MM-dd HH:mm:ss",
                          "createdBy": "",
                          "updatedBy": "",
                          "updateTime": "yyyy-MM-dd HH:mm:ss",
                          "tenantId": "",
                          "scopes": [
                            "api"
                          ],
                          "createAt": 0,
                          "active": true,
                          "revoked": true
                       }
                    }""";
            Map<String, String> privateToken = new HashMap<>();
            privateToken.put("userAccount", JSONObject.parseObject(json).getJSONObject("data").getString("userId"));
            privateToken.put("token", JSONObject.parseObject(json).getJSONObject("data").getString("accessToken"));
            return privateToken;
        }
        String uri = ApiGwUriUtil.toUri("/cmdevops-code/server/openapi/private-token/default" + (userId != null ? "?userAccount=" + userId : ""));
        Map<String, String> headers = new HashMap<>();
        headers.put(X_TENANT_ID, tenantId);
        ApiGwResult<BaseResponse> result = null;
        try {
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, BaseResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("推送个人Token异常");
        }

        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            System.out.println("响应数据：" + result.getBody());
            JSONObject data = JSON.parseObject(JSON.toJSONString(result.getBody())).getJSONObject("data");
            Map<String, String> privateToken = new HashMap<>();
            privateToken.put("userAccount", data.getString("userAccount"));
            privateToken.put("token", data.getString("accessToken"));
            return privateToken;
        } else {
            System.err.println("调用失败!错误码:" + result.getStatus());
            return null;
        }
    }

    @Override
    public JSONArray getCodeRepositoryList(String tenantId, String spaceId, String userId) {
        if (isDev) {
            ClassPathResource resource = new ClassPathResource("META-INF/code/template/codeRepositoryList.json");
            try (InputStream input = resource.getInputStream()) {
                String text = new String(input.readAllBytes(), StandardCharsets.UTF_8);
                return JSONObject.parseObject(text).getJSONArray("data");
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        String uri = ApiGwUriUtil.toUri("/cmdevops-code/server/openapi/projects/page?pageNo=1&pageSize=9999&spaceCode=" + spaceId + "&userAccount=" + userId);
        ApiGwResult<JSONObject> result = null;
        Map<String, String> headers = new HashMap<>();
        headers.put(X_TENANT_ID, tenantId);

        try {
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, JSONObject.class);
            if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
                JSONArray codeList = JSON.parseObject(JSON.toJSONString(result.getBody())).getJSONArray("data");
                for (int i = 0; i < codeList.size(); i++) {
                    JSONObject co = codeList.getJSONObject(i);
                    co.put("id", co.get("id") + "");
                }
                System.out.println("响应数据：" + result.getBody());
                return codeList;
            } else {
                System.err.println("调用失败!错误码:" + result.getStatus());
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("获取代码仓库列表异常");
        }
    }

    @Override
    public JSONObject getCodeMetadata(String tenantId, String projectId) {
        if (isDev) {
            ClassPathResource resource = new ClassPathResource("META-INF/code/template/codeMetadata.json");
            try (InputStream input = resource.getInputStream()) {
                String text = new String(input.readAllBytes(), StandardCharsets.UTF_8);
                JSONArray data = JSONArray.parseArray(text);
                for (int i = 0; i < data.size(); i++) {
                    JSONObject co = data.getJSONObject(i);
                    if (co.getString("id").equals(projectId)) {
                        return co.getJSONObject("data");
                    }
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        String uri = ApiGwUriUtil.toUri("/cmdevops-code/server/openapi/projects/" + projectId + "/metadata");
        ApiGwResult<BaseResponse> result = null;
        Map<String, String> headers = new HashMap<>();
        headers.put(X_TENANT_ID, tenantId);

        try {
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, BaseResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("推送元数据异常");
        }

        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            System.out.println("响应数据：" + result.getBody());
            return JSON.parseObject(JSON.toJSONString(result.getBody())).getJSONObject("data");
        } else {
            System.err.println("调用失败!错误码:" + result.getStatus());
            return null;
        }
    }


    @Override
    public JSONArray getUserList(String tenantId, String spaceId) {
        if (isDev) {
            String json = """
                    {
                        "success":true,
                        "code": "SUCCESS",
                        "message": "操作成功",
                        "data":
                            [
                                {
                                    "userUid":"USER259836433257201664",
                                    "userName":"系统用户",
                                    "userEmail":"<EMAIL>",
                                    "roles":[
                                        {
                                            "roleId":"112",
                                            "roleName":"系统成员"
                                        },
                                        {
                                            "roleId":"113",
                                            "roleName":"研发经理"
                                        }
                                    ]
                                },
                                {
                                    "userUid":"zhaost_a",
                                    "userName":"赵书廷",
                                    "userEmail":"<EMAIL>",
                                    "roles":[
                                        {
                                            "roleId":"112",
                                            "roleName":"系统成员"
                                        },
                                        {
                                            "roleId":"114",
                                            "roleName":"产品经理"
                                        }
                                    ]
                                },
                                {
                                    "userUid":"licf_csd",
                                    "userName":"李昌峰",
                                    "userEmail":"<EMAIL>",
                                    "roles":[
                                        {
                                            "roleId":"112",
                                            "roleName":"系统成员"
                                        },
                                        {
                                            "roleId":"114",
                                            "roleName":"产品经理"
                                        }
                                    ]
                                }
                            ],
                        "count": 2
                    }""";
            return JSONObject.parseObject(json).getJSONArray("data");
        }
        String uri = ApiGwUriUtil.toUri("/cmdevops-platform/space/openapi/space/member/page?tenantId=" + tenantId + "&spaceId=" + spaceId + "&pageNo=1&pageSize=999999&appKey=CMDEVOPS-CI");
        Map<String, String> headers = new HashMap<>();
        headers.put(X_TENANT_ID, tenantId);
        ApiGwResult<BaseResponse> result = null;
        try {
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, BaseResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("获取研发空间成员列表异常");
        }

        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            System.out.println("响应数据：" + result.getBody());
            JSONArray userList = JSON.parseObject(JSON.toJSONString(result.getBody())).getJSONArray("data");
            return userList;
        } else {
            System.err.println("调用失败!错误码:" + result.getStatus());
            return null;
        }
    }

    @Override
    public JSONObject getCodeRepoMetaData(String projectId, String tenantId) {
        if (isDev) {
            String msg = """
                    {
                       "success": true,
                       "code": "",
                       "message": "",
                       "data": {
                         "httpPath": "",
                         "sshPath": "",
                         "systemName": "持续集成系统",
                         "systemCode": "cmdevops-ci",
                         "apps": [
                             {
                                "applicationCode": "123",
                                "appName": "四共应用平台"
                             },
                             {
                                "applicationCode": "1234",
                                "appName": "四共应用平台1"
                             }
                           ],
                         "spaceName": "",
                         "spaceCode": "",
                         "projects": [
                            {
                               "projectCode": "project1",
                               "projectName": "项目1"
                            },
                            {
                               "projectCode": "project2",
                               "projectName": "项目2"
                            },
                         ]
                       }
                     }
                    """;

            return JSONObject.parseObject(msg).getJSONObject("data");

        }
        String uri = ApiGwUriUtil.toUri("/cmdevops-code/server/openapi/projects/" + projectId + "/metadata");
        ApiGwResult<BaseResponse> result = null;
        Map<String, String> headers = new HashMap<>();
        headers.put(X_TENANT_ID, tenantId);

        try {
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, BaseResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("获取代码仓库元数据异常");
        }

        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            System.out.println("响应数据：" + result.getBody());
            return JSON.parseObject(JSON.toJSONString(result.getBody())).getJSONObject("data");
        } else {
            System.err.println("调用失败!错误码:" + result.getStatus());
            return null;
        }
    }

    @Override
    public JSONArray queryAccelerationNode(String source, String tenantId) {
        if (isDev) {
            String json = "";
            if (source.equals("tenant")) {
                json = """
                    {
                        "success":true,
                        "code": "SUCCESS",
                        "message": "操作成功",
                        "data":
                            [
                                {
                                    "nodeId":"tenant1",
                                    "nodeName":"租户级1",
                                },
                                {
                                    "nodeId":"tenant2",
                                    "nodeName":"租户级2",
                                },
                                {
                                    "nodeId":"tenant3",
                                    "nodeName":"租户级3",
                                }
                            ],
                        "count": 2
                    }""";
            } else {
                json = """
                    {
                        "success":true,
                        "code": "SUCCESS",
                        "message": "操作成功",
                        "data":
                            [
                                {
                                   "nodeId":"platform1",
                                    "nodeName":"平台级1",
                                },
                                {
                                    "nodeId":"platform2",
                                    "nodeName":"平台级2",
                                },
                                {
                                    "nodeId":"platform3",
                                    "nodeName":"平台级3",
                                }
                            ],
                        "count": 2
                    }""";
            }
            return JSONObject.parseObject(json).getJSONArray("data");
        }
        return null;
    }

    @Override
    public JSONArray querySpaceList(String tenantId) {
        if (isDev) {
            String json = """
                    {
                        "success":true,
                        "code": "SUCCESS",
                        "message": "操作成功",
                        "data":
                            [
                                {
                                    "spaceName":"四共XXX研发空间1",
                                    "spaceCode":"ZJYFKJ202506051147001",
                                    "spaceOwnerId":"1",
                                    "spaceOwnerName":"张三",
                                    "spaceState":"0",
                                    "templateId":"1",
                                    "templateName":"敏捷模板",
                                    "spaceDesc":"四共XXX研发空间描述"
                                },
                                {
                                    "spaceName":"四共XXX研发空间2",
                                    "spaceCode":"ZJYFKJ202506051147002",
                                    "spaceOwnerId":"1",
                                    "spaceOwnerName":"张三",
                                    "spaceState":"0",
                                    "templateId":"1",
                                    "templateName":"敏捷模板",
                                    "spaceDesc":"四共XXX研发空间描述"
                                }
                              ],
                        "count": 2
                    }""";
            return JSONObject.parseObject(json).getJSONArray("data");
        }
        String uri = ApiGwUriUtil.toUri("/cmdevops-platform/space/openapi/space/list?tenantId=" + tenantId + "&appKey=CMDEVOPS-CI");
        Map<String, String> headers = new HashMap<>();
        headers.put(X_TENANT_ID, tenantId);
        ApiGwResult<BaseResponse> result = null;
        try {
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, BaseResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("获取研发空间列表异常");
        }

        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            System.out.println("响应数据：" + result.getBody());
            return JSON.parseObject(JSON.toJSONString(result.getBody())).getJSONArray("data");
        } else {
            System.err.println("调用失败!错误码:" + result.getStatus());
            return null;
        }
    }

    @Override
    public JSONArray queryProjectList(String tenantId) {
        if (isDev) {
            String json = """
                    {
                        "success":true,
                        "code": "SUCCESS",
                        "message": "操作成功",
                        "data":
                            [
                                {
                                    "projectCode":"1",
                                    "projectName":"四共项目管理项目",
                                    "projectStatus":"2",
                                    "projectManagerName":"张三",
                                    "projectSource":"RMS"
                                },
                                {
                                    "projectCode":"2",
                                    "projectName":"四共研发桌面项目",
                                    "projectStatus":"1",
                                    "projectManagerName":"李四",
                                    "projectSource":"SELF"
                                }
                            ]
                    }""";
            return JSONObject.parseObject(json).getJSONArray("data");
        }
        String uri = ApiGwUriUtil.toUri("/cmdevops-project/server/openapi/project/list?tenantId=" + tenantId + "&appKey=CMDEVOPS-CI");
        Map<String, String> headers = new HashMap<>();
        headers.put(X_TENANT_ID, tenantId);
        ApiGwResult<BaseResponse> result = null;
        try {
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, BaseResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("获取项目列表异常");
        }

        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            System.out.println("响应数据：" + result.getBody());
            return JSON.parseObject(JSON.toJSONString(result.getBody())).getJSONArray("data");
        } else {
            System.err.println("调用失败!错误码:" + result.getStatus());
            return null;
        }
    }

    @Override
    public JSONArray queryProjectSpaceRel(String tenantId, String projectId) {
        if (isDev) {
            String json = """
                    {
                        "success":true,
                        "code": "SUCCESS",
                        "message": "操作成功",
                        "data":
                            [
                                {
                                    "spaceName":"四共XXX研发空间1",
                                    "spaceCode":"ZJYFKJ202506051147001",
                                    "spaceOwnerId":"1",
                                    "spaceOwnerName":"张三",
                                    "spaceState":"0",
                                    "templateId":"1",
                                    "templateName":"敏捷模板",
                                    "spaceDesc":"四共XXX研发空间描述"
                                },
                                {
                                    "spaceName":"四共XXX研发空间2",
                                    "spaceCode":"ZJYFKJ202506051147002",
                                    "spaceOwnerId":"1",
                                    "spaceOwnerName":"张三",
                                    "spaceState":"0",
                                    "templateId":"1",
                                    "templateName":"敏捷模板",
                                    "spaceDesc":"四共XXX研发空间描述"
                                }
                              ],
                        "count": 2
                    }""";
            return JSONObject.parseObject(json).getJSONArray("data");
        }
        String uri = ApiGwUriUtil.toUri("/cmdevops-platform/space/openapi/project/space/page?tenantId=" + tenantId + "&appKey=CMDEVOPS-CI&pageNo=1&pageSize=999999&projectCode=" + projectId);
        Map<String, String> headers = new HashMap<>();
        headers.put(X_TENANT_ID, tenantId);
        ApiGwResult<BaseResponse> result = null;
        try {
            result = ApiGwClient.invoke(uri, ApiGwHttpMethod.GET, headers, null, BaseResponse.class);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("获取项目关联空间列表异常");
        }

        if (EmptyValidator.isNotEmpty(result) && ApiGwHttpStatus.SUCCESS == result.getStatus()) {
            System.out.println("响应数据：" + result.getBody());
            return JSON.parseObject(JSON.toJSONString(result.getBody())).getJSONArray("data");
        } else {
            System.err.println("调用失败!错误码:" + result.getStatus());
            return null;
        }
    }
}
