build:
  tools:
    android:
      - gradle7.2#10.86.12.11:20200/devops-test/android-sdkman:v1
      - gradle7.3#10.86.12.11:20200/devops-test/android-sdkman:v1
      - gradle7.4#10.86.12.11:20200/devops-test/android-sdkman:v1
      - gradle8.0#10.86.12.11:20200/devops-test/android-sdkman:v1
      - gradle8.5#10.86.12.11:20200/devops-test/android-sdkman:v1
    ant:
      - ant1.9.7#10.86.12.11:20200/devops-test/ant-sdkman:v1
      - ant1.10.1#10.86.12.11:20200/devops-test/ant-sdkman:v1
      - ant1.10.3#10.86.12.11:20200/devops-test/ant-sdkman:v1
      - ant1.10.13#10.86.12.11:20200/devops-test/ant-sdkman:v1
    cangjie:
      - cangjie-0.50.3#10.86.12.11:20200/devops-test/cangjie:0.50.3
    compScan:
      - cdxgen-deno#10.86.12.11:20200/devops-test/cdxgen-deno:master
    cmake:
      - cmake3.10.1-gcc5.5.0#10.86.12.11:20200/devops-test/cmake:cmake3.10.1-gcc5.5.0
      - cmake2.8.12-gcc4.8.5#10.86.12.11:20200/devops-test/cmake:cmake2.8.12-gcc4.8.5
      - cmake2.8.12-gcc5.5.0#10.86.12.11:20200/devops-test/cmake:cmake2.8.12-gcc5.5.0
      - cmake2.8.12-gcc6.4.0#10.86.12.11:20200/devops-test/cmake:cmake2.8.12-gcc6.4.0
      - cmake-ccache-icecream:cmake3.10.1-gcc5.5.0#10.86.12.11:20200/devops-test/cmake-ccache-icecream:cmake3.10.1-gcc5.5.0
    fis:
      - fis1.0.2#10.86.12.11:20200/devops-test/fis:1.0.2
      - fis1.0.6#10.86.12.11:20200/devops-test/fis:1.0.6
    go:
      - go1.16.1#10.86.12.11:20200/devops-test/golang-goenv:v1
      - go1.20.1#10.86.12.11:20200/devops-test/golang-goenv:v1
      - go1.21.1#10.86.12.11:20200/devops-test/golang-goenv:v1
      - go1.22.1#10.86.12.11:20200/devops-test/golang-goenv:v1
      - go1.23.1#10.86.12.11:20200/devops-test/golang-goenv:v1
    gradle:
      - gradle7.2#10.86.12.11:20200/devops-test/gradle-sdkman:v1
      - gradle7.3#10.86.12.11:20200/devops-test/gradle-sdkman:v1
      - gradle7.4#10.86.12.11:20200/devops-test/gradle-sdkman:v1
      - gradle8.0#10.86.12.11:20200/devops-test/gradle-sdkman:v1
      - gradle8.5#10.86.12.11:20200/devops-test/gradle-sdkman:v1
    icecream:
      - icecream-centos7:20250930#10.86.12.11:20200/devops-test/icecream-centos7:20250930
    jdk:
      - jdk8#10.86.12.11:20200/devops-test/maven-sdkman:v1
      - jdk11#10.86.12.11:20200/devops-test/maven-sdkman:v1
      - jdk17#10.86.12.11:20200/devops-test/maven-sdkman:v1
      - jdk21#10.86.12.11:20200/devops-test/maven-sdkman:v1
    lua:
      - lua:5.4#10.86.12.11:20200/devops/lua:5.4
    maven:
      - maven3.6.3#10.86.12.11:20200/devops-test/maven-sdkman:v1
      - maven3.8.8#10.86.12.11:20200/devops-test/maven-sdkman:v1
      - maven3.9.5#10.86.12.11:20200/devops-test/maven-sdkman:v1
    msbuild:
      - msbuild17-dotnetcore8.0#10.86.12.11:20200/devops-test/msbuild:msbuild17-dotnetcore8.0
    node:
      - node12.12.0#10.86.12.11:20200/devops-test/node-nvm:v1
      - node14.21.3#10.86.12.11:20200/devops-test/node-nvm:v1
      - node16.20.2#10.86.12.11:20200/devops-test/node-nvm:v1
      - node18.20.8#10.86.12.11:20200/devops-test/node-nvm:v1
    p4:
      - p4c:latest#10.86.12.11:20200/devops-test/p4c:v1.0
    pack:
      - pack2.0.0#10.86.12.11:20200/devops-test/pack:4.0.0
    php:
      - php7.3.33#10.86.12.11:20200/devops-test/php-phpbrew:v1
      - php8.0.30#10.86.12.11:20200/devops-test/php-phpbrew:v1
    pyinstaller:
      - python3.7.10#10.86.12.11:20200/devops-test/python:3.7.10
    ruby:
      - ruby:3.2.2#10.86.12.11:20200/devops-test/ruby:3.2.2
    rust:
      - cargo:1.72.0#10.86.12.11:20200/devops/cargo:1.72.0
    scala:
      - sbt:1.3.2-jdk1.8#10.86.12.11:20200/devops/scala:v1
    setuptools:
      - python3.7.10#10.86.12.11:20200/devops-test/python:3.7.10
    shell:
      - shell4.2.46#10.86.12.11:20200/devops-test/shell:4.2.46
    sonar:
      - sonar#10.86.12.11:20200/devops-test/sonar-scanner:6.1

