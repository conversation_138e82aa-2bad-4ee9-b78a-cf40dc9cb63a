nacos:
  config:
    remote-first: true
    bootstrap:
      enable: true
      log-enable: true
    server-addr: ${NACOS_CONFIG_SERVER_ADDR:127.0.0.1:8848}
    namespace: cmdevops
    data-ids: common-config.yaml
    group: cmdevops-common
    type: yaml
    auto-refresh: true
    max-retry: 10
    username: ${NACOS_CONFIG_USERNAME:nacos}
    password: ${NACOS_CONFIG_PASSWORD:nacos}
    extConfig:
      - namespace: ${NACOS_CONFIG_EXT_NAMESPACE:cmdevops-ci}
        data-ids: cmdevops-ci-server.yaml
        group: cmdevops-ci
        type: yaml
        auto-refresh: true

springdoc:
  default-flat-param-object: true
  model-converters:
    deprecating-converter:
      enabled: true
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true

jasypt:
  encryptor:
    password: ${JASYPT_ENCRYPTOR_PASSWORD:HD&DCJJDGDK}
    iv-generator-classname: ${JASYPT_ENCRYPTOR_CLASSNAME:org.jasypt.iv.NoIvGenerator}
    algorithm: ${JASYPT_ENCRYPTOR_ALGORITHM:PBEWithMD5AndTripleDES}
    pool-size: 5

spring:
  profiles:
    include:
      - dynamic
      - static
      - common
      - tool
  application:
    name: cmdevops-ci-server
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
  task:
    execution:
      pool:
        core-size: 8
        max-size: 100
        queue-capacity: 100

server:
  port: ${server_port:8080}
  servlet:
    session:
      timeout: PT8H
  tomcat:
    connection-timeout: 30s
    keep-alive-timeout: 300s
    accesslog:
      enabled: true
      pattern: 'TID: %{traceparent}i %h %l %u [%{yyyy-MM-dd HH:mm:ss:SSS}t] %r %s %b'
      directory: ${logging.file.path}/accesslog
      prefix: accesslog
      suffix: .log
      fileDateFormat: _yyyyMMdd
      rotate: true
      max-days: 7
  error:
    include-message: always

app:
  context-path: "/cmdevops-ci/server"
  base-package: "com.cmcc.cmdevops.ci"

---
spring:
  config:
    activate:
      on-profile: local
management:
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
  endpoints:
    web:
      exposure:
        include: ["*"]
  otlp:
    metrics:
      export:
        enabled: false

