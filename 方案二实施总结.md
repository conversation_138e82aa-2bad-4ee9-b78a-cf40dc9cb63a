# 方案二实施总结：分离执行环境

## 问题背景
在重构后的分布式构建中，出现了`docker-compose: command not found`错误，原因是icecream初始化step在CMake构建的Docker容器内执行，但该容器缺少docker-compose命令。

## 解决方案
实施方案二：分离执行环境
- icecream初始化step在主agent环境执行（有docker-compose命令）
- CMake构建step在Docker容器环境执行（有编译工具）

## 实施细节

### 1. 架构变更
```
重构前：
stage {
    agent { docker { ... } }  // 整个stage在Docker容器中
    steps {
        script { icecream初始化 }  // 缺少docker-compose
        script { CMake构建 }
    }
}

重构后：
stage {
    steps {
        script {                    // 主agent环境
            icecream初始化           // 有docker-compose命令
        }
        script {
            withDockerContainer {   // Docker容器环境
                CMake构建           // 有编译工具
            }
        }
    }
}
```

### 2. 方法重构
- `generateCombinedSteps`: 支持不同执行环境的step组合
- `generateHostStep`: 生成主agent环境的step
- `generateDockerStep`: 生成Docker容器环境的step
- `generateIcecreamInitStepContent`: 只生成内容，不包含环境包装
- `generateCmakeBuildStepContent`: 只生成内容，不包含环境包装

### 3. Jenkins Pipeline语法
使用`withDockerContainer`指令在steps内启动Docker容器：
```groovy
script {
    withDockerContainer(image: 'image:tag', args: 'docker-args') {
        // 构建逻辑
    }
}
```

## 生成的Pipeline结构

### 启用分布式构建时
```groovy
stage('CMake构建-1') {
    steps {
        script {
            echo 'Step 1: icecream分布式集群环境初始化（主agent环境）'
            // 在主agent上执行docker-compose命令
            // 启动icecream调度器和工作节点
        }
        script {
            echo 'Step 2: CMake构建（Docker容器环境）'
            withDockerContainer(image: 'cmake-image', args: 'docker-args') {
                // 在Docker容器中执行CMake构建
                // 连接到icecream调度器进行分布式编译
            }
        }
    }
}
```

### 未启用分布式构建时
```groovy
stage('CMake构建-1') {
    steps {
        script {
            echo 'Step 2: CMake构建（Docker容器环境）'
            withDockerContainer(image: 'cmake-image', args: 'docker-args') {
                // 在Docker容器中执行普通CMake构建
            }
        }
    }
}
```

## 优势
1. **解决环境问题**：icecream初始化在有docker-compose的主agent环境执行
2. **保持功能完整**：CMake构建在专门的编译容器中执行
3. **符合需求**：仍然是单个stage中的多个step
4. **灵活性好**：不同step可以使用最适合的执行环境

## 技术要点
1. **环境分离**：主agent vs Docker容器
2. **命令可用性**：docker-compose在主agent，编译工具在容器
3. **网络连通性**：使用--network host确保容器间通信
4. **Jenkins语法**：正确使用withDockerContainer指令
