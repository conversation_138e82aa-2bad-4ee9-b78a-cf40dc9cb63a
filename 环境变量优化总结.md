# 环境变量优化总结

## 问题背景
用户指出dockerArgs应该在所有步骤之前，因为分布式构建需要用到其中的环境变量。原来的实现中，环境变量只在CMake构建step中可用，但icecream初始化step也需要这些环境变量。

## 优化方案
将环境变量的设置提前到所有step之前，确保在icecream初始化和CMake构建两个step中都可用。

## 实施细节

### 1. 环境变量设置顺序
```
原来：
steps {
    script { icecream初始化 }           // 无环境变量
    script {
        withDockerContainer(...) {      // 有环境变量
            CMake构建
        }
    }
}

优化后：
steps {
    script {                            // 设置全局环境变量
        env.USE_DISTRIBUTED_BUILD = 'true/false'
        env.ICECC_SCHEDULER = ''        // 待设置
    }
    script { icecream初始化 }           // 可用环境变量
    script {
        withDockerContainer(...) {      // 可用环境变量
            CMake构建
        }
    }
}
```

### 2. 环境变量生命周期
1. **初始化阶段**：设置USE_DISTRIBUTED_BUILD
2. **icecream初始化阶段**：使用USE_DISTRIBUTED_BUILD，设置ICECC_SCHEDULER
3. **CMake构建阶段**：使用USE_DISTRIBUTED_BUILD和ICECC_SCHEDULER

### 3. 代码变更

#### generateCombinedSteps方法
```java
// 在所有step之前设置全局环境变量
steps.append("script {\n")
        .append("echo '设置构建环境变量'\n")
        .append("env.USE_DISTRIBUTED_BUILD = '").append(enableDistributedBuild ? "true" : "false").append("'\n")
        .append("echo \"USE_DISTRIBUTED_BUILD: ${env.USE_DISTRIBUTED_BUILD}\"\n");

if (enableDistributedBuild) {
    steps.append("env.ICECC_SCHEDULER = ''\n")  // 将在icecream初始化后设置
            .append("echo \"ICECC_SCHEDULER will be set after icecream initialization\"\n");
}

steps.append("} \n");
```

#### icecream初始化内容
```groovy
// icecream初始化完成后，设置调度器地址环境变量
def hostIP = sh(script: "hostname -I | awk '{print $1}'", returnStdout: true).trim()
env.ICECC_SCHEDULER = "${hostIP}:8765"
echo "设置ICECC_SCHEDULER: ${env.ICECC_SCHEDULER}"
```

#### 分布式构建脚本
```groovy
// 使用已设置的环境变量
echo "使用调度器地址: ${env.ICECC_SCHEDULER}"
echo "分布式构建状态: ${env.USE_DISTRIBUTED_BUILD}"

withEnv([
    "USE_DISTRIBUTED_BUILD=${env.USE_DISTRIBUTED_BUILD}",
    "ICECC_SCHEDULER=${env.ICECC_SCHEDULER}"
]) {
    // 构建逻辑
}
```

## 生成的Pipeline结构

### 启用分布式构建时
```groovy
stage('CMake构建-1') {
    steps {
        script {
            echo '设置构建环境变量'
            env.USE_DISTRIBUTED_BUILD = 'true'
            env.ICECC_SCHEDULER = ''
            echo "USE_DISTRIBUTED_BUILD: ${env.USE_DISTRIBUTED_BUILD}"
        }
        script {
            echo 'Step 1: icecream分布式集群环境初始化（主agent环境）'
            echo "当前USE_DISTRIBUTED_BUILD: ${env.USE_DISTRIBUTED_BUILD}"
            // icecream初始化逻辑
            // 设置env.ICECC_SCHEDULER
        }
        script {
            echo 'Step 2: CMake构建（Docker容器环境）'
            withDockerContainer(...) {
                // 使用env.USE_DISTRIBUTED_BUILD和env.ICECC_SCHEDULER
                // CMake构建逻辑
            }
        }
    }
}
```

## 优势
1. **环境变量一致性**：所有step都能访问相同的环境变量
2. **逻辑清晰**：环境变量的设置和使用顺序明确
3. **避免重复**：不需要在每个step中重新获取IP地址
4. **符合需求**：dockerArgs中的环境变量在所有步骤之前设置

## 技术要点
1. **Jenkins环境变量**：使用env.VARIABLE_NAME设置全局环境变量
2. **变量传递**：通过withEnv传递给Docker容器
3. **动态设置**：ICECC_SCHEDULER在icecream初始化后动态设置
4. **生命周期管理**：确保变量在需要时已经设置完成
